{"name": "youniverse", "version": "0.0.0", "private": true, "description": "", "author": "", "license": "UNLICENSED", "scripts": {"setup": "./devops/scripts/setup-env.sh", "dev": "turbo run dev", "dev:prod": "turbo run dev:prod", "dev:youapi": "turbo run dev --filter=youapi...", "env": "node devops/scripts/doppler-pull.js", "env:prod": "node devops/scripts/doppler-pull.js --env=production", "generate:api": "pnpm --filter=@repo/api run generate", "config": "node devops/scripts/doppler-pull-config.js", "config:prod": "node devops/scripts/doppler-pull-config.js --env=production", "build": "turbo run build", "build:local": "turbo run build --no-cache", "build:cf:local": "turbo run build:cf --no-cache", "build:packages:local": "turbo run build --filter=@repo/* --no-cache", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "turbo run format", "format:check": "turbo run format:check", "typecheck": "turbo run typecheck", "prepare": "husky", "deps-analysis": "tsx scripts/deps-analysis.ts"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@repo/typescript-config": "workspace:*", "@rsbuild/core": "^1.4.2", "@rsbuild/plugin-less": "^1.2.5", "@rsbuild/plugin-react": "^1.3.2", "@tailwindcss/postcss": "^4.1.11", "@types/highlight.js": "^10.1.0", "@types/jest": "^29.5.14", "@types/node": "catalog:", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "connect-injector": "^0.4.4", "dotenv": "^17.2.0", "dotenv-expand": "^12.0.2", "dotenv-flow": "^4.1.0", "drizzle-kit": "^0.24.2", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "rsbuild-plugin-tailwindcss": "^0.2.2", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "turbo": "^2.5.5", "typescript": "catalog:", "madge": "^8.0.0"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=22"}}