#!/usr/bin/env node
/**
 * 部署 YouRoute 路由规则
 * - Worker Routes: 管理 deploymentStrategy: 'worker-routes' 的应用
 * - Origin Rules: 管理 deploymentStrategy: 'origin-rules' 的后端服务
 */

import { ROUTES } from '../src/routes.js';
import { createDeployConfig } from './lib/config.js';
import { deployRulesAtomically, generateGroupedOriginRules } from './lib/origin-rules.js';
import { classifyRoutes } from './lib/route-classifier.js';
import { generateTransformRules } from './lib/transform-rules.js';
import { deployWorkerRoutesAtomically } from './lib/worker-routes.js';

async function deployRules() {
  // 1. 获取环境配置
  const config = createDeployConfig();

  // 2. 分类路由
  const { workerRoutes, originRoutes } = classifyRoutes(ROUTES);

  console.log(`📋 Deploying routes for ${config.ENVIRONMENT}:`);
  console.log(`   • Worker Routes: ${workerRoutes.length} workers`);
  console.log(`   • Origin Rules: ${originRoutes.length} backend services`);

  // 4. 生成 Origin Rules
  const rules = generateGroupedOriginRules(config, originRoutes);
  console.log(`🔧 Generated ${rules.length} Origin Rules for backend services`);

  // 5. 生成 Transform Rules
  const transformRules = generateTransformRules(config, originRoutes);
  console.log(`🔧 Generated ${transformRules.length} Transform Rules`);

  // 6. 原子性部署
  try {
    // Deploy Worker Routes first
    if (workerRoutes.length > 0) {
      await deployWorkerRoutesAtomically(config, workerRoutes);
    }

    // Deploy Origin Rules for backend services
    if (rules.length > 0) {
      await deployRulesAtomically(config, rules, 'http_request_origin');
    }

    // Deploy Transform Rules (if any)
    if (transformRules.length > 0) {
      await deployRulesAtomically(config, transformRules, 'http_request_late_transform');
    }

    console.log('✅ All routes deployed successfully!');
    console.log(`📊 Deployment Summary:`);
    console.log(`   • Environment: ${config.ENVIRONMENT}`);
    console.log(`   • Worker routes: ${workerRoutes.length} workers`);
    console.log(`   • Origin rules: ${rules.length} backend services`);
    console.log(`   • Transform rules: ${transformRules.length}`);
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  }
}

// Run the script
deployRules().catch(console.error);
