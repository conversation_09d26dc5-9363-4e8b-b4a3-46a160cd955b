/**
 * 配置管理工具
 * 处理环境配置解析和域名模式获取
 */

import { readFileSync } from 'node:fs';
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';

/**
 * 获取环境特定的主机名条件（基于 wrangler.toml 的域名配置）
 */
export function getHostnameCondition(config) {
  const conditions = config.DOMAIN_PATTERNS.map((pattern) =>
    pattern.includes('*')
      ? `http.request.headers["host"][0] matches "${pattern.replace(/\*/g, '.*')}"`
      : `http.request.headers["host"][0] eq "${pattern}"`,
  );

  return `(${conditions.join(' or ')})`;
}

/**
 * 从 wrangler.toml 解析域名模式
 */
export function parseWranglerDomains(environment) {
  try {
    const __dirname = dirname(fileURLToPath(import.meta.url));
    const wranglerPath = join(__dirname, '../../wrangler.toml');
    const wranglerContent = readFileSync(wranglerPath, 'utf8');

    // 查找对应环境的 routes 配置
    const envSection = environment === 'production' ? 'env.production' : 'env.preview';
    const envRegex = new RegExp(`\\[${envSection}\\]([\\s\\S]*?)(?=\\n\\[|$)`, 'i');
    const envMatch = wranglerContent.match(envRegex);

    if (!envMatch) {
      throw new Error(`⚠️  No [${envSection}] section found in wrangler.toml`);
    }

    // 提取 routes 中的 pattern
    const routesRegex = /pattern\s*=\s*"([^"]+)"/g;
    const domains = [];

    const matches = [...envMatch[1].matchAll(routesRegex)];
    for (const match of matches) {
      const pattern = match[1];
      // 移除 /* 或 / 后缀，获取域名
      const domain = pattern.replace(/\/(\*)?$/, '');
      domains.push(domain);
    }

    if (domains.length === 0) {
      throw new Error(`⚠️  No route patterns found for ${environment}`);
    }

    console.log(`📋 Parsed domains from wrangler.toml for ${environment}: ${domains.join(', ')}`);
    return domains;
  } catch (error) {
    throw new Error(`⚠️  Failed to parse wrangler.toml: ${error}`);
  }
}

/**
 * 创建部署配置对象
 */
export function createDeployConfig() {
  const environment = process.env.ENVIRONMENT || 'production';

  const config = {
    CLOUDFLARE_API_TOKEN: process.env.CLOUDFLARE_API_TOKEN,
    CLOUDFLARE_ZONE_ID: process.env.CLOUDFLARE_ZONE_ID,
    ENVIRONMENT: environment,
    // 从 wrangler.toml 自动解析域名模式
    DOMAIN_PATTERNS: parseWranglerDomains(environment),
  };

  if (!config.CLOUDFLARE_API_TOKEN || !config.CLOUDFLARE_ZONE_ID) {
    console.error('❌ Missing required environment variables:');
    console.error('   - CLOUDFLARE_API_TOKEN');
    console.error('   - CLOUDFLARE_ZONE_ID');
    console.error('');
    console.error('💡 Domain patterns are auto-parsed from wrangler.toml routes configuration');
    process.exit(1);
  }

  return config;
}
