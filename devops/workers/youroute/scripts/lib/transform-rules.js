/**
 * Transform Rules 管理
 * 处理 Cloudflare Transform Rules 的生成和配置
 */

import { getHostnameCondition } from './config.js';

/**
 * 生成分组的 Transform Rules - 为有 headers 配置的后端路由生成规则
 */
export function generateTransformRules(config, routes) {
  // routes 参数现在已经是经过分类的后端路由
  const routesWithHeaders = routes.filter((route) => route.headers);

  // 为每个有头部的路由创建一个合并的规则
  return routesWithHeaders.map((route) => {
    // 创建路径表达式 - 使用 OR 条件组合该路由的所有路径
    const pathExpressions = route.paths.map((path) =>
      path === '/'
        ? `(http.request.uri.path eq "/")`
        : `(starts_with(http.request.uri.path, "${path}"))`,
    );

    const pathCondition =
      pathExpressions.length === 1 ? pathExpressions[0] : `(${pathExpressions.join(' or ')})`;

    // 添加环境特定的主机名条件
    const hostnameCondition = getHostnameCondition(config);

    // 组合主机名和路径条件
    const combinedExpression = `(${hostnameCondition}) and ${pathCondition}`;

    return {
      ref: `youroute_headers_${config.ENVIRONMENT}_${route.name}`,
      expression: combinedExpression,
      description: `[${config.ENVIRONMENT.toUpperCase()}] Headers for [${route.paths.join(', ')}] (${route.name})`,
      action: 'rewrite',
      action_parameters: {
        headers: Object.entries(route.headers).reduce((acc, [name, value]) => {
          acc[name] = {
            operation: 'set',
            // Check if value looks like an expression (contains dots/brackets)
            ...(value.includes('.') || value.includes('[') ? { expression: value } : { value }),
          };
          return acc;
        }, {}),
      },
    };
  });
}
