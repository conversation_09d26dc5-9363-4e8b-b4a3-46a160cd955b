/**
 * 路由分类工具
 * 简单地根据 deploymentStrategy 字段分类路由
 */

/**
 * 根据 deploymentStrategy 分类路由
 */
export function classifyRoutes(routes) {
  // 基本验证
  for (const route of routes) {
    if (!route.deploymentStrategy) {
      throw new Error(
        `Route '${route.name}' missing required 'deploymentStrategy' field. Add 'deploymentStrategy: "worker-routes"' or 'deploymentStrategy: "origin-rules"'`,
      );
    }

    if (!['worker-routes', 'origin-rules'].includes(route.deploymentStrategy)) {
      throw new Error(
        `Route '${route.name}' has invalid deploymentStrategy: '${route.deploymentStrategy}'. Valid values: 'worker-routes', 'origin-rules'`,
      );
    }
  }

  // 简单分类
  const workerRoutes = routes.filter((route) => route.deploymentStrategy === 'worker-routes');
  const originRoutes = routes.filter((route) => route.deploymentStrategy === 'origin-rules');

  return {
    workerRoutes,
    originRoutes,
  };
}
