/**
 * Worker Routes API 管理
 * 处理 Worker Routes 的创建、删除和原子性部署
 */

/**
 * 为 Worker 生成路由模式
 */
export function generateWorkerRoutePatterns(route, environment, config) {
  // 严格验证输入参数
  if (!route || typeof route !== 'object') {
    throw new Error(`Invalid route object: ${JSON.stringify(route)}`);
  }
  if (!route.name || typeof route.name !== 'string') {
    throw new Error(`Route missing valid 'name' field: ${JSON.stringify(route)}`);
  }
  if (!route.paths || !Array.isArray(route.paths) || route.paths.length === 0) {
    throw new Error(
      `Route '${route.name}' missing valid 'paths' array: ${JSON.stringify(route.paths)}`,
    );
  }
  if (!environment || typeof environment !== 'string') {
    throw new Error(`Invalid environment: ${JSON.stringify(environment)}`);
  }
  if (
    !config ||
    !config.DOMAIN_PATTERNS ||
    !Array.isArray(config.DOMAIN_PATTERNS) ||
    config.DOMAIN_PATTERNS.length === 0
  ) {
    throw new Error(
      `Invalid config.DOMAIN_PATTERNS: ${JSON.stringify(config?.DOMAIN_PATTERNS)}. Check wrangler.toml parsing.`,
    );
  }

  // 使用 config 中解析的域名（来自 wrangler.toml）
  const baseDomain = config.DOMAIN_PATTERNS[0]; // 取第一个域名作为基础域名

  return route.paths.map((path) => {
    // 根据路径生成模式
    let pattern;
    if (path === '/') {
      pattern = `${baseDomain}/`;
    } else {
      // 移除开头的 /，并在末尾添加通配符（如果需要）
      const cleanPath = path.startsWith('/') ? path.slice(1) : path;
      pattern = `${baseDomain}/${cleanPath}${path.endsWith('*') ? '' : '*'}`;
    }

    return {
      pattern,
      script: `${route.name}-${environment}`,
      zone_name: 'youmind.com',
    };
  });
}

/**
 * 获取指定 Zone 的所有 Worker Routes
 */
export async function listWorkerRoutes(config) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/workers/routes`,
    {
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to list worker routes: HTTP ${response.status}: ${error}`);
  }

  const data = await response.json();
  if (!data.success) {
    throw new Error(`Cloudflare API error: ${data.errors.map((e) => e.message).join(', ')}`);
  }

  return data.result || [];
}

/**
 * 创建 Worker Route
 */
export async function createWorkerRoute(config, routePattern) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/workers/routes`,
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pattern: routePattern.pattern,
        script: routePattern.script,
      }),
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(
      `Failed to create worker route ${routePattern.pattern}: HTTP ${response.status}: ${error}`,
    );
  }

  const data = await response.json();
  if (!data.success) {
    throw new Error(`Cloudflare API error: ${data.errors.map((e) => e.message).join(', ')}`);
  }

  return data.result;
}

/**
 * 删除 Worker Route
 */
export async function deleteWorkerRoute(config, routeId) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/workers/routes/${routeId}`,
    {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to delete worker route ${routeId}: HTTP ${response.status}: ${error}`);
  }

  const data = await response.json();
  if (!data.success) {
    throw new Error(`Cloudflare API error: ${data.errors.map((e) => e.message).join(', ')}`);
  }

  return data.result;
}

/**
 * 原子性部署 Worker Routes
 */
export async function deployWorkerRoutesAtomically(config, workerRoutes) {
  // 严格验证配置参数 - 在任何 API 调用前失败
  if (!config) {
    throw new Error('Missing config object');
  }
  if (!config.CLOUDFLARE_API_TOKEN || typeof config.CLOUDFLARE_API_TOKEN !== 'string') {
    throw new Error('Missing or invalid CLOUDFLARE_API_TOKEN');
  }
  if (!config.CLOUDFLARE_ZONE_ID || typeof config.CLOUDFLARE_ZONE_ID !== 'string') {
    throw new Error('Missing or invalid CLOUDFLARE_ZONE_ID');
  }
  if (!config.ENVIRONMENT || typeof config.ENVIRONMENT !== 'string') {
    throw new Error('Missing or invalid ENVIRONMENT');
  }
  if (
    !config.DOMAIN_PATTERNS ||
    !Array.isArray(config.DOMAIN_PATTERNS) ||
    config.DOMAIN_PATTERNS.length === 0
  ) {
    throw new Error('Missing or invalid DOMAIN_PATTERNS - check wrangler.toml parsing');
  }

  // 验证 workerRoutes 参数
  if (!Array.isArray(workerRoutes)) {
    throw new Error(`workerRoutes must be an array, got: ${typeof workerRoutes}`);
  }

  if (workerRoutes.length === 0) {
    console.log('🔄 No worker routes to deploy');
    return;
  }

  // 验证每个 worker route 的基本结构
  for (const route of workerRoutes) {
    if (!route || typeof route !== 'object') {
      throw new Error(`Invalid worker route: ${JSON.stringify(route)}`);
    }
    if (!route.name || typeof route.name !== 'string') {
      throw new Error(`Worker route missing 'name': ${JSON.stringify(route)}`);
    }
    if (!route.deploymentStrategy || route.deploymentStrategy !== 'worker-routes') {
      throw new Error(
        `Worker route '${route.name}' must have deploymentStrategy: 'worker-routes', got: ${route.deploymentStrategy}`,
      );
    }
  }

  console.log(`🔍 Deploying Worker Routes for ${workerRoutes.length} workers...`);

  // 1. 获取当前所有 Worker Routes
  const existingRoutes = await listWorkerRoutes(config);

  // 验证 API 响应格式
  if (!Array.isArray(existingRoutes)) {
    throw new Error(
      `Unexpected API response format from listWorkerRoutes: ${typeof existingRoutes}`,
    );
  }

  // 2. 识别我们管理的路由（基于 script 名称模式）
  const managedWorkerNames = new Set(workerRoutes.map((r) => `${r.name}-${config.ENVIRONMENT}`));
  const managedRoutes = existingRoutes.filter(
    (route) => route.script && managedWorkerNames.has(route.script),
  );

  console.log(
    `📋 Found ${existingRoutes.length} existing routes, ${managedRoutes.length} managed by us`,
  );

  // 3. 生成新的路由配置
  const newRoutes = [];
  for (const route of workerRoutes) {
    const patterns = generateWorkerRoutePatterns(route, config.ENVIRONMENT, config);
    newRoutes.push(...patterns);
  }

  console.log(`🔧 Generated ${newRoutes.length} new worker routes`);

  // 3.5. 验证生成的路由模式 - 确保没有异常模式
  for (const route of newRoutes) {
    if (!route.pattern || typeof route.pattern !== 'string') {
      throw new Error(`Invalid generated pattern: ${JSON.stringify(route)}`);
    }
    if (!route.script || typeof route.script !== 'string') {
      throw new Error(`Invalid generated script name: ${JSON.stringify(route)}`);
    }
    if (!route.pattern.includes('.')) {
      throw new Error(`Suspicious pattern (no domain): ${route.pattern}`);
    }
    if (route.pattern.includes('undefined') || route.pattern.includes('null')) {
      throw new Error(`Pattern contains undefined/null: ${route.pattern}`);
    }
  }

  // 4. 检测重复模式，避免创建冲突
  const existingPatterns = new Set(existingRoutes.map((r) => r.pattern));
  const routesToCreate = newRoutes.filter((route) => !existingPatterns.has(route.pattern));
  const routesToSkip = newRoutes.filter((route) => existingPatterns.has(route.pattern));

  if (routesToSkip.length > 0) {
    console.log(`⏭️  Skipping ${routesToSkip.length} routes (patterns already exist):`);
    routesToSkip.forEach((route) => console.log(`   • ${route.pattern} → ${route.script}`));
  }

  const createdRoutes = []; // 在外部声明，便于回滚时访问

  try {
    // 5. 创建新路由 (仅创建不重复的，避免停机时间)
    if (routesToCreate.length > 0) {
      console.log(`✨ Creating ${routesToCreate.length} new routes...`);
      for (const routePattern of routesToCreate) {
        const created = await createWorkerRoute(config, routePattern);
        createdRoutes.push(created);
      }
    } else {
      console.log(`📋 No new routes to create (all patterns already exist)`);
    }

    // 6. 删除冗余的旧路由 (删除与当前期望配置不匹配的路由)
    const expectedPatterns = new Set(newRoutes.map((r) => r.pattern));
    const routesToDelete = managedRoutes.filter((route) => !expectedPatterns.has(route.pattern));

    if (routesToDelete.length > 0) {
      console.log(`🗑️  Cleaning up ${routesToDelete.length} replaced routes...`);
      for (const route of routesToDelete) {
        await deleteWorkerRoute(config, route.id);
      }
    } else {
      console.log(`📋 No old routes to clean up (all routes were skipped duplicates)`);
    }

    console.log(`✅ Worker Routes deployed successfully!`);
    console.log(`📊 Summary:`);
    console.log(`   • Created: ${createdRoutes.length} routes`);
    console.log(`   • Deleted: ${routesToDelete.length} routes`);
    console.log(`   • Skipped: ${routesToSkip.length} routes (duplicates)`);

    return createdRoutes;
  } catch (error) {
    console.error('❌ Worker Routes deployment failed:', error.message);
    console.error(
      '🔍 Failure point: After creating',
      createdRoutes.length,
      'routes, before completing cleanup',
    );

    try {
      // 如果新路由已创建但后续失败，清理新创建的路由
      if (createdRoutes && createdRoutes.length > 0) {
        console.log(`🧹 Cleaning up ${createdRoutes.length} newly created routes...`);
        for (const route of createdRoutes) {
          if (!route.id) {
            console.warn(`⚠️  Route missing ID, cannot delete: ${JSON.stringify(route)}`);
            continue;
          }
          await deleteWorkerRoute(config, route.id);
        }
        console.log('✅ Successfully cleaned up failed deployment');
      } else {
        console.log('📋 No cleanup needed - old routes remain intact');
      }
    } catch (rollbackError) {
      console.error('💥 Rollback cleanup failed:', rollbackError.message);
      console.error('⚠️  Manual route cleanup may be required - check Cloudflare dashboard');
      console.error(
        '🔧 Created routes that may need manual deletion:',
        createdRoutes.map((r) => r.pattern || r.id).join(', '),
      );
    }

    // Re-throw with context
    throw new Error(
      `Worker Routes deployment failed: ${error.message}. Check logs above for cleanup status.`,
    );
  }
}
