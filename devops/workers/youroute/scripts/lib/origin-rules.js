/**
 * Origin Rules 管理
 * 处理 Cloudflare Origin Rules 的创建、更新和原子性部署
 */

import { getHostnameCondition } from './config.js';

/**
 * 生成分组的 Origin Rules - 按目标域名分组，使用 OR 条件（仅用于后端服务）
 */
export function generateGroupedOriginRules(config, routes) {
  // routes 参数现在已经是经过分类的后端路由

  // 按目标域名分组路由
  const routesByTarget = new Map();

  routes.forEach((route) => {
    const targetDomain = route.target.domains[config.ENVIRONMENT];

    if (!routesByTarget.has(targetDomain)) {
      routesByTarget.set(targetDomain, { routes: [], allPaths: [] });
    }

    const targetGroup = routesByTarget.get(targetDomain);
    targetGroup.routes.push(route);
    targetGroup.allPaths.push(...route.paths);
  });

  // 为每个目标创建一个合并的规则
  return Array.from(routesByTarget.entries()).map(([targetDomain, { routes, allPaths }]) => {
    // 创建路径表达式 - 使用 OR 条件组合所有路径
    const pathExpressions = allPaths.map((path) =>
      path === '/'
        ? `(http.request.uri.path eq "/")`
        : `(starts_with(http.request.uri.path, "${path}"))`,
    );

    const pathCondition =
      pathExpressions.length === 1 ? pathExpressions[0] : `(${pathExpressions.join(' or ')})`;

    // 添加环境特定的主机名条件以避免冲突
    const hostnameCondition = getHostnameCondition(config);

    // 组合主机名和路径条件
    const combinedExpression = `(${hostnameCondition}) and ${pathCondition}`;

    // 生成稳定的引用ID
    const targetId = targetDomain.replace(/[^a-zA-Z0-9]/g, '_');
    const routeNames = routes.map((r) => r.name).join('_');

    return {
      ref: `youroute_${config.ENVIRONMENT}_${targetId}`,
      expression: combinedExpression,
      description: `[${config.ENVIRONMENT.toUpperCase()}] Route [${allPaths.join(', ')}] to ${targetDomain} (${routeNames})`,
      action: 'route',
      action_parameters: {
        origin: {
          host: targetDomain,
        },
        host_header: targetDomain,
      },
    };
  });
}

/**
 * 查找现有的 ruleset by phase
 */
export async function findExistingRuleset(config, phase) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/rulesets`,
    {
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to list rulesets: HTTP ${response.status}: ${error}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(`Cloudflare API error: ${data.errors.map((e) => e.message).join(', ')}`);
  }

  // 查找指定阶段的 zone 级别 ruleset
  return data.result.find((ruleset) => ruleset.phase === phase && ruleset.kind === 'zone') || null;
}

/**
 * 原子性更新现有 ruleset
 */
export async function updateRulesetAtomically(config, rulesetId, rules, phase, existingRuleset) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/rulesets/${rulesetId}`,
    {
      method: 'PUT', // PUT 确保原子性更新
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: existingRuleset.name, // 保持原有名称，不要修改系统 ruleset 的名称
        description: `YouRoute ${phase.replace('http_request_', '').replace('_', ' ')} Rules for ${config.ENVIRONMENT} environment - Updated ${new Date().toISOString()}`,
        rules,
      }),
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to update ruleset: HTTP ${response.status}: ${error}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(`Cloudflare API error: ${data.errors.map((e) => e.message).join(', ')}`);
  }
}

/**
 * 创建新的 ruleset
 */
export async function createNewRuleset(config, rules, phase) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/rulesets`,
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: `youroute-${phase.replace('http_request_', '')}-${config.ENVIRONMENT}`,
        description: `YouRoute ${phase.replace('http_request_', '').replace('_', ' ')} Rules for ${config.ENVIRONMENT} environment - Created ${new Date().toISOString()}`,
        kind: 'zone',
        phase: phase,
        rules,
      }),
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to create ruleset: HTTP ${response.status}: ${error}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(`Cloudflare API error: ${data.errors.map((e) => e.message).join(', ')}`);
  }

  return data.result;
}

/**
 * 回滚到之前的 ruleset 状态
 */
export async function rollbackRuleset(config, rulesetId, backupRuleset) {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${config.CLOUDFLARE_ZONE_ID}/rulesets/${rulesetId}`,
    {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${config.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: backupRuleset.name,
        rules: backupRuleset.rules,
      }),
    },
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to rollback ruleset: HTTP ${response.status}: ${error}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(
      `Cloudflare API rollback error: ${data.errors.map((e) => e.message).join(', ')}`,
    );
  }
}

/**
 * 原子性部署规则 - 要么全部成功，要么全部失败
 */
export async function deployRulesAtomically(config, rules, phase) {
  const ruleTypeName = phase === 'http_request_origin' ? 'Origin Rules' : 'Transform Rules';
  console.log(`🔍 Checking for existing ${ruleTypeName} ruleset...`);

  // 1. 检查是否已存在指定阶段的 ruleset
  const existingRuleset = await findExistingRuleset(config, phase);

  let backupRuleset = null;

  try {
    if (existingRuleset) {
      console.log(`📦 Found existing ruleset: ${existingRuleset.name} (ID: ${existingRuleset.id})`);
      console.log(`🔄 Updating existing ruleset atomically...`);

      // 备份当前状态用于回滚
      backupRuleset = existingRuleset;

      // 使用 PUT 原子性更新整个 ruleset
      await updateRulesetAtomically(config, existingRuleset.id, rules, phase, existingRuleset);

      console.log(`✅ Ruleset updated successfully (Version: ${existingRuleset.version} → New)`);
    } else {
      console.log('📝 No existing ruleset found, creating new one...');

      // 创建新的 ruleset
      const newRuleset = await createNewRuleset(config, rules, phase);

      console.log(`✅ New ruleset created successfully (ID: ${newRuleset.id})`);
    }
  } catch (error) {
    // 如果更新失败且有备份，尝试回滚
    if (backupRuleset && existingRuleset) {
      console.warn('⚠️  Update failed, attempting rollback...');
      try {
        await rollbackRuleset(config, existingRuleset.id, backupRuleset);
        console.log('✅ Successfully rolled back to previous state');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError);
        console.error('⚠️  Manual intervention may be required');
      }
    }
    throw error;
  }
}
