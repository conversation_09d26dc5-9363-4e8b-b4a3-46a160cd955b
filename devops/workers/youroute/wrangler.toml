main = "src/index.ts"
compatibility_date = "2025-07-01"
compatibility_flags = ["nodejs_compat", "global_fetch_strictly_public", "nodejs_compat_populate_process_env"]

# 生产环境配置
[env.production]
name = "youroute-production"
routes = [
  { pattern = "em2025.youmind.com/", zone_name = "youmind.com" }
]

[env.production.vars]
ENVIRONMENT = "production"
LOG_LEVEL = "info"

[[env.production.services]]
binding = "YOUHOME"
service = "youhome-production"

# 预览环境配置
[env.preview]
name = "youroute-preview"
routes = [
  { pattern = "em2025-preview.youmind.com/", zone_name = "youmind.com" }
]

[env.preview.vars]
ENVIRONMENT = "preview"
LOG_LEVEL = "debug"

[[env.preview.services]]
binding = "YOUHOME"
service = "youhome-preview"

# 本地开发环境变量
[vars]
ENVIRONMENT = "development"
LOG_LEVEL = "debug"

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true
