{"name": "youroute", "version": "2.0.0", "private": true, "description": "Multi-app routing worker for youniverse monorepo", "scripts": {"dev": "wrangler dev", "deploy:preview": "wrangler deploy --env preview", "deploy:production": "wrangler deploy --env production", "build": "tsc", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "type-check": "tsc --noEmit", "deploy:rules:preview": "ENVIRONMENT=preview node scripts/deploy-rules.js", "deploy:rules:production": "ENVIRONMENT=production node scripts/deploy-rules.js"}, "devDependencies": {"@cloudflare/workers-types": "catalog:", "@types/node": "catalog:", "typescript": "catalog:", "wrangler": "catalog:"}}