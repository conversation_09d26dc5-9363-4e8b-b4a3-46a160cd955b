{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": false, "outDir": "dist", "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "declaration": false, "declarationMap": false, "sourceMap": false, "types": ["@cloudflare/workers-types", "node"]}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}