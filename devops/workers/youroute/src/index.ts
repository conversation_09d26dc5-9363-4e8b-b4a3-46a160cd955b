/**
 * YouRoute - Multi-App Routing Worker
 */

import { isUserAuthenticated } from './auth';
import type { Env } from './types';
import { createRedirectResponse, log } from './utils';

/**
 * 通过服务绑定调用 youhome
 */
async function callYouhome(request: Request, env: Env): Promise<Response> {
  log('info', 'Calling youhome via service binding', env);

  try {
    const response = await env.YOUHOME.fetch(request);

    // 添加路由信息头部
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });

    newResponse.headers.set('X-Routed-By', 'youroute-worker');
    newResponse.headers.set('X-Route-Target', 'youhome-service-binding');
    newResponse.headers.set('X-Environment', env.ENVIRONMENT || 'unknown');

    return newResponse;
  } catch (error) {
    log('error', `Failed to call youhome service: ${error}`, env);

    return new Response('Service temporarily unavailable', {
      status: 503,
      headers: {
        'X-Routed-By': 'youroute-worker',
        'X-Route-Error': 'service-binding-failed',
      },
    });
  }
}

export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    try {
      const isAuthenticated = isUserAuthenticated(request, env);
      if (isAuthenticated) {
        // 用户已认证，重定向到 /boards
        log('info', `User authenticated, redirecting to /boards`, env);
        return createRedirectResponse('/boards', env);
      }

      // 用户未认证，调用 youhome
      log('info', 'User not authenticated, calling youhome', env);
      return await callYouhome(request, env);
    } catch (error) {
      // 认证检查失败，记录错误但继续调用 youhome
      log('warn', `Auth check failed for root path: ${error}, falling back to youhome`, env);
      return await callYouhome(request, env);
    }
  },
};
