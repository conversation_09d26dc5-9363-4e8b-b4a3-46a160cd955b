/**
 * YouRoute Worker Utilities
 */

import type { Env, LogLevel } from './types';
import { LOG_LEVELS, YOUROUTE_VERSION } from './types';

/**
 * 简化的日志工具函数 - 减少冗余日志
 */
export function log(level: LogLevel, message: string, env: Env): void {
  const currentLevel = LOG_LEVELS[env.LOG_LEVEL as LogLevel] ?? LOG_LEVELS.info;
  if (LOG_LEVELS[level] >= currentLevel) {
    console[level === 'debug' ? 'log' : level](`[${level.toUpperCase()}] ${message}`);
  }
}

/**
 * 创建重定向响应
 */
export function createRedirectResponse(location: string, env: Env): Response {
  log('info', `Redirecting to: ${location}`, env);

  return new Response(null, {
    status: 302,
    headers: {
      Location: location,
      'X-Routed-By': 'youroute-worker',
      'X-Route-Target': 'redirect',
      'X-Environment': env.ENVIRONMENT || 'unknown',
      'X-Youroute-Version': YOUROUTE_VERSION,
    },
  });
}
