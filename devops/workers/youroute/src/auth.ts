/**
 * YouRoute Worker Authentication
 */

import type { Env } from './types';
import { log } from './utils';

/**
 * 检查用户是否已认证
 * 简单检查 Supabase 认证 cookies 是否存在，不进行 token 验证或刷新
 */
export function isUserAuthenticated(request: Request, env: Env): boolean {
  try {
    // 解析请求的 cookies
    const cookieHeader = request.headers.get('cookie') || '';
    const cookies: Record<string, string> = {};

    cookieHeader.split(';').forEach((cookie) => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });

    // 检查 Supabase 关键认证 cookies 是否存在
    // 这些是 Supabase 用于存储 session 的标准 cookie 名称
    const hasAccessToken =
      cookies['sb-access-token'] || cookies['supabase-auth-token'] || cookies.YOUMIND_MOBILE_AUTH; // 自定义 mobile auth cookie

    // 也可以检查是否有 Supabase session cookies (通常以项目引用开头)
    const hasSessionCookie = Object.keys(cookies).some(
      (name) => name.startsWith('sb-') && name.includes('-auth-token'),
    );

    const isAuthenticated = !!(hasAccessToken || hasSessionCookie);

    if (isAuthenticated) {
      log('debug', 'User has auth cookies, considered authenticated', env);
    } else {
      log('debug', 'No auth cookies found', env);
    }

    return isAuthenticated;
  } catch (error) {
    log('warn', `Auth check failed: ${error}`, env);
    return false;
  }
}
