import {
  Atom,
  Getter,
  atom as jotaiAtom,
  SetStateAction,
  Setter,
  useSetAtom,
  WritableAtom,
} from 'jotai';
import { useEffect } from 'react';

type Callback<Value> = (get: Getter, set: Setter, newVal: Value, prevVal: Value) => void;

export const scopedAtoms: Atom<unknown>[] = [];

export const scopedAtom = ((...args: Parameters<typeof jotaiAtom>) => {
  const atomReturn = jotaiAtom(...args);
  scopedAtoms.push(atomReturn);
  return atomReturn;
}) as typeof jotaiAtom;

export function scopedAtomWithListener<Value>(
  initialValue: Value,
): readonly [
  WritableAtom<Value, [SetStateAction<Value>], void>,
  (callback: Callback<Value>) => void,
];

// 支持 read/write 模式的重载
export function scopedAtomWithListener<Value, Args extends unknown[], Result>(
  read: (get: Getter) => Value,
  write: (get: Getter, set: Setter, ...args: Args) => Result,
): readonly [WritableAtom<Value, Args, Result>, (callback: Callback<Value>) => void];

export function scopedAtomWithListener<
  Value,
  Args extends unknown[] = [SetStateAction<Value>],
  Result = void,
>(...args: Parameters<typeof jotaiAtom>) {
  const baseAtom = scopedAtom(...args);
  const listenersAtom = scopedAtom<Callback<Value>[]>([]);

  const anAtom = scopedAtom(
    (get) => get(baseAtom),
    (get, set, newValue: Value) => {
      const prevVal = get(baseAtom) as Value;
      const result = set(baseAtom, newValue);

      // 只有当值发生变化时才触发监听器
      if (prevVal !== newValue) {
        get(listenersAtom).forEach((callback) => {
          callback(get, set, newValue, prevVal);
        });
      }

      return result;
    },
  );

  const useListener = (callback: Callback<Value>) => {
    const setListeners = useSetAtom(listenersAtom);
    useEffect(() => {
      setListeners((prev) => [...prev, callback]);
      return () =>
        setListeners((prev) => {
          const index = prev.indexOf(callback);
          return [...prev.slice(0, index), ...prev.slice(index + 1)];
        });
    }, [setListeners, callback]);
  };

  return [anAtom, useListener] as const;
}
