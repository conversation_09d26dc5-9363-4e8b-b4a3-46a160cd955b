{"name": "@repo/ui", "version": "0.0.0", "private": true, "license": "MIT", "files": ["src"], "exports": {".": "./src/index.ts", "./globals.css": "./src/styles/globals.css", "./components/*": "./src/components/*", "./lib/*": "./src/lib/*.ts", "./lib/posthog/*": "./src/lib/posthog/*", "./hooks/*": "./src/hooks/*.ts"}, "scripts": {"build": "echo '✅ @repo/ui: No build needed - TypeScript source files consumed directly'", "shadcn:add": "pnpm dlx shadcn@latest add", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format ."}, "dependencies": {"@infinite-canvas-tutorial/ecs": "0.0.1-alpha.36", "@infinite-canvas-tutorial/webcomponents": "0.0.1-alpha.36", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@repo/common": "workspace:*", "antd": "catalog:", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "copy-image-clipboard": "^2.1.2", "howler": "catalog:", "lucide-react": "catalog:", "next-themes": "^0.4.6", "posthog-js": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-hook-form": "catalog:", "react-router-dom": "catalog:", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "use-debounce": "^10.0.5", "usehooks-ts": "catalog:", "vaul": "^1.1.2", "jotai": "catalog:"}, "devDependencies": {"@types/howler": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7", "typescript": "catalog:"}}