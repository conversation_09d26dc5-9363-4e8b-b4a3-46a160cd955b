// config/snip-config-schema.ts

import { z } from 'zod';
import { onlineVideoConfig } from './modules/online-video/config';

// 全局配置 Schema
export const SnipConfigSchema = z.object({
  options: z.object({
    readonly: z.boolean().default(false),
  }),

  // 预留，后续用于配置组件，比如 ImagePreview，组件传入 props 需要有类型检测
  components: z.object({}),

  // 全局事件
  events: z.object({
    onNeedRefreshSnip: z.function().returns(z.union([z.void(), z.promise(z.void())])),
  }),

  onlineVideo: onlineVideoConfig.schema,
});

// 类型推导
export type SnipConfig = z.infer<typeof SnipConfigSchema>;

// 外部需要传入的配置（必需 + 可选覆盖）
export const ExternalSnipConfigSchema = SnipConfigSchema.pick({
  events: true,
}).extend({
  options: SnipConfigSchema.shape.options.optional(),
});

export const defaultConfig: SnipConfig = SnipConfigSchema.parse({});

export type ExternalSnipConfig = z.infer<typeof ExternalSnipConfigSchema>;

// 合并 externalConfig 和 defaultConfig
export function createConfig(externalConfig: ExternalSnipConfig): SnipConfig {
  return SnipConfigSchema.parse(externalConfig);
}
