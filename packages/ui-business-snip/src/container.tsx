import { SnipTypeEnum } from '@repo/common/types/snip/types';
import { useAtom } from 'jotai';
import { snipDetailAtom } from './atoms';
import { useSnipContext } from './context';
import { OnlineVideo } from './modules/online-video';

export function SnipContainer() {
  const [snip] = useAtom(snipDetailAtom);
  switch (snip?.type) {
    case SnipTypeEnum.VIDEO:
      return <OnlineVideo />;
    // case SnipTypeEnum.ARTICLE:
    //   return <Article readonly={readonly} showOpenSource={showOpenSource} />;

    // case SnipTypeEnum.VOICE:
    //   return <Podcast readonly={readonly} />;
    // case SnipTypeEnum.SNIPPET:
    //   return <TextSnippet />;
    // case SnipTypeEnum.UNKNOWN_WEBPAGE:
    //   return <Unknown readonly={readonly} />;
    // case SnipTypeEnum.OTHER_WEBPAGE:
    //   return <Other />;
    // case SnipTypeEnum.IMAGE:
    //   return <Image />;
    // case SnipTypeEnum.PDF:
    //   return <PDF />;
    // case SnipTypeEnum.OFFICE:
    //   return <TextFileContent />;
    // case SnipTypeEnum.TEXT_FILE:
    //   return <TextFileContent />;
    // default:
    //   return <></>;
  }
}
