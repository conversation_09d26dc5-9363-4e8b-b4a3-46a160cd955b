import { SnipConfig } from './config';
import { SnipContext } from './context';

export function SnipProvider({
  config,
  children,
}: {
  config: SnipConfig;
  children: React.ReactNode;
}) {
  return <SnipContext.Provider value={config}>{children}</SnipContext.Provider>;
}

export { createConfig } from './config';
export { SnipContainer } from './container';
export { useSnipContext } from './context';
