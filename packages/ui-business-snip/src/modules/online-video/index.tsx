import { useAtom } from 'jotai';
import { useSnipContext } from '../../context';
import { hasTranscriptAtom } from './atoms';

export function OnlineVideo() {
  // 从上下文获取在线视频配置
  const { onlineVideo } = useSnipContext();
  const { options } = onlineVideo;

  // 使用本模块的 atoms
  const [hasTranscript, setHasTranscript] = useAtom(hasTranscriptAtom);

  const handleToggleTranscript = () => {
    setHasTranscript(!hasTranscript);
  };

  return (
    <div className="online-video-container">
      <h3>Online Video Component</h3>
      <div>
        <label>
          <input type="checkbox" checked={hasTranscript} onChange={handleToggleTranscript} />
          有字幕
        </label>
      </div>
      <div>配置状态: {JSON.stringify(options)}</div>
    </div>
  );
}
