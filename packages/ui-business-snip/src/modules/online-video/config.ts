import z from 'zod';
import { defineMaterialConfigs } from '../../utils';
import { hasTranscriptAtom } from './atoms';

export const onlineVideoConfig = defineMaterialConfigs({
  options: z.object({
    // 可以添加在线视频的配置选项
    autoPlay: z.boolean().default(false),
    showControls: z.boolean().default(true),
  }),
  events: z.object({
    // 可以添加在线视频的事件处理
    onVideoLoad: z.function().optional(),
    onVideoError: z.function().optional(),
  }),
});

// 导出atoms供其他地方使用
export const onlineVideoAtoms = {
  hasTranscript: hasTranscriptAtom,
};
