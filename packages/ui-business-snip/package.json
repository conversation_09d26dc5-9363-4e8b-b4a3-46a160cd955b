{"name": "@repo/ui-business-snip", "version": "0.0.0", "private": true, "license": "MIT", "files": ["src"], "exports": {".": "./src/index.tsx", "./*": "./src/*"}, "scripts": {"build": "echo '✅ @repo/ui-business-snip: No build needed - TypeScript source files consumed directly'", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format ."}, "dependencies": {"antd": "catalog:", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "copy-image-clipboard": "^2.1.2", "howler": "catalog:", "lucide-react": "catalog:", "next-themes": "^0.4.6", "posthog-js": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-hook-form": "catalog:", "react-router-dom": "catalog:", "tailwind-merge": "^2.6.0", "use-debounce": "^10.0.5", "usehooks-ts": "catalog:", "@repo/ui": "workspace:*", "@repo/common": "workspace:*", "zod": "catalog:", "jotai": "catalog:"}, "devDependencies": {"@types/react": "catalog:", "@types/react-dom": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7", "typescript": "catalog:"}}