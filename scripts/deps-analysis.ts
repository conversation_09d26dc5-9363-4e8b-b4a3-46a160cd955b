import fs from 'fs';
import madge from 'madge';

// const PATH = 'apps/youweb/src/components/icon/quote.tsx';
const PATH = 'apps/youweb/src/components/snip/snip-content/text-file.tsx';
(async () => {
  const res = await madge(PATH, {
    tsConfig: 'apps/youweb/tsconfig.json',
    extensions: ['ts', 'tsx'],
    excludeRegExp: [/node_modules/],
  });

  const tree = res.obj();

  // 要排除的路径前缀
  const excludePrefix = '../../../../../../packages';

  // 构建过滤后的树
  const filteredTree = {};

  Object.keys(tree).forEach((file) => {
    // 跳过外部包的文件
    if (file.startsWith(excludePrefix)) return;

    // 过滤依赖：移除外部包的依赖
    const deps = (tree[file] || []).filter((dep) => !dep.startsWith(excludePrefix));

    // 保留所有内部文件，即使没有依赖
    filteredTree[file] = deps;
  });

  // 输出为 JSON
  fs.writeFileSync('deps-filtered.json', JSON.stringify(filteredTree, null, 2));

  console.log('生成完成：deps-filtered.json');
  console.log(`原始文件数: ${Object.keys(tree).length}`);
  console.log(`过滤后文件数: ${Object.keys(filteredTree).length}`);
  console.log(`过滤掉的外部包文件: ${Object.keys(tree).length - Object.keys(filteredTree).length}`);
})();
