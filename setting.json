{"editor.fontFamily": "Maple Mono NF CN, Menlo, Monaco, 'Courier New', monospace", "editor.fontLigatures": "'calt', 'cv01', 'ss01', 'zero'", "editor.fontSize": 12.5, "editor.lineHeight": 2.6, "editor.lineNumbers": "relative", "editor.tabSize": 2, "editor.indentSize": 2, "editor.smoothScrolling": true, "editor.cursorSmoothCaretAnimation": "on", "editor.cursorBlinking": "solid", "editor.cursorSurroundingLines": 10, "editor.bracketPairColorization.enabled": true, "editor.matchBrackets": "never", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.minimap.autohide": true, "editor.minimap.scale": 2, "editor.minimap.renderCharacters": false, "terminal.integrated.scrollback": 9999999999999999999999999, "terminal.integrated.fontSize": 12, "terminal.integrated.lineHeight": 1.5, "files.autoSave": "onFocusChange", "files.autoSaveWhenNoErrors": false, "files.saveConflictResolution": "overwriteFileOnDisk", "workbench.statusBar.visible": true, "workbench.list.smoothScrolling": true, "workbench.editor.enablePreviewFromQuickOpen": true, "workbench.editor.focusRecentEditorAfterClose": true, "workbench.editor.editorActionsLocation": "titleBar", "workbench.layoutControl.enabled": false, "workbench.startupEditor": "none", "workbench.colorTheme": "Solarized Dark", "workbench.productIconTheme": "fluent-icons", "workbench.iconTheme": "symbols", "window.titleBarStyle": "custom", "window.commandCenter": false, "window.zoomLevel": 0.55, "explorer.confirmDelete": false, "search.followSymlinks": false, "security.workspace.trust.untrustedFiles": "open", "notebook.formatOnSave.enabled": true, "diffEditor.ignoreTrimWhitespace": false, "diffEditor.maxComputationTime": 0, "animations.Tabs": "Slide", "animations.Scrolling": "Fade", "animations.Durations": {"Command-Palette": 500, "Tabs": 500, "Scrolling": 500, "Smooth-Mode": 500, "Active": 500}, "indentRainbow.colors": ["rgba(38, 70, 83,0.15)", "rgba(42, 157, 143,0.15)", "rgba(233, 196, 106, 0.15)", "rgba(244, 162, 97,0.15)", "rgba(231, 111, 81,0.15)", "rgba(231, 111, 81,0.1)", "rgba(231, 111, 81,0.05)", "rgba(231, 111, 81,0.025)"], "workbench.colorCustomizations": {"minimap.background": "#00000000", "tab.inactiveBackground": "#0c0e14", "[Vira*]": {"toolbar.activeBackground": "#80CBC426", "button.background": "#80CBC4", "button.hoverBackground": "#80CBC4cc", "extensionButton.separator": "#80CBC433", "extensionButton.background": "#80CBC414", "extensionButton.foreground": "#80CBC4", "extensionButton.hoverBackground": "#80CBC433", "extensionButton.prominentForeground": "#80CBC4", "extensionButton.prominentBackground": "#80CBC414", "extensionButton.prominentHoverBackground": "#80CBC433", "activityBarBadge.background": "#80CBC4", "activityBar.activeBorder": "#80CBC4", "activityBarTop.activeBorder": "#80CBC4", "list.inactiveSelectionIconForeground": "#80CBC4", "list.activeSelectionForeground": "#80CBC4", "list.inactiveSelectionForeground": "#80CBC4", "list.highlightForeground": "#80CBC4", "sash.hoverBorder": "#80CBC480", "list.activeSelectionIconForeground": "#80CBC4", "scrollbarSlider.activeBackground": "#80CBC480", "editorSuggestWidget.highlightForeground": "#80CBC4", "textLink.foreground": "#80CBC4", "progressBar.background": "#80CBC4", "pickerGroup.foreground": "#80CBC4", "tab.activeBorder": "#80CBC4", "notificationLink.foreground": "#80CBC4", "editorWidget.resizeBorder": "#80CBC4", "editorWidget.border": "#80CBC4", "settings.modifiedItemIndicator": "#80CBC4", "panelTitle.activeBorder": "#80CBC4", "breadcrumb.activeSelectionForeground": "#80CBC4", "menu.selectionForeground": "#80CBC4", "menubar.selectionForeground": "#80CBC4", "editor.findMatchBorder": "#80CBC4", "selection.background": "#80CBC440", "statusBarItem.remoteBackground": "#80CBC414", "statusBarItem.remoteHoverBackground": "#80CBC4", "statusBarItem.remoteForeground": "#80CBC4", "notebook.inactiveFocusedCellBorder": "#80CBC480", "commandCenter.activeBorder": "#80CBC480", "chat.slashCommandForeground": "#80CBC4", "chat.avatarForeground": "#80CBC4", "activityBarBadge.foreground": "#000000", "button.foreground": "#000000", "statusBarItem.remoteHoverForeground": "#000000"}}, "vim.useSystemClipboard": true, "vim.useCtrlKeys": false, "vim.handleKeys": {"<Esc>": false}, "vim.easymotion": true, "vim.timeout": 300, "vim.easymotionMarkerForegroundColorOneChar": "#ffeb3b", "vim.easymotionMarkerForegroundColorTwoCharFirst": "#ff9800", "vim.easymotionMarkerForegroundColorTwoCharSecond": "#ff6f00", "vim.autoSwitchInputMethod.enable": true, "vim.autoSwitchInputMethod.enableWindow": true, "vim.autoSwitchInputMethod.defaultIM": "com.apple.keylayout.ABC", "vim.autoSwitchInputMethod.insertLeaveIM": "com.apple.keylayout.ABC", "vim.autoSwitchInputMethod.insertModeIM": "com.apple.keylayout.ABC", "vim.autoSwitchInputMethod.obtainIMCmd": "/opt/homebrew/bin/im-select", "vim.autoSwitchInputMethod.switchIMCmd": "/opt/homebrew/bin/im-select {im}", "vim.insertModeKeyBindings": [{"before": ["j", "j"], "after": ["<Esc>"]}], "vim.normalModeKeyBindingsNonRecursive": [{"before": ["<space>", "l"], "after": ["$"]}, {"before": ["<space>", "h"], "after": ["^"]}, {"before": ["<space>", "w", "w"], "commands": ["workbench.action.navigateEditorGroups"]}, {"before": [" ", "q"], "after": [":", "w", "q", "<CR>"]}, {"before": [" ", "t"], "commands": ["cursorTop"]}, {"before": [" ", ";"], "commands": ["editor.action.revealDefinition"]}, {"before": [" ", "'"], "commands": ["workbench.action.navigateBack"]}, {"before": [" ", "i"], "commands": ["workbench.action.nextEditor"]}, {"before": [" ", "u"], "commands": ["workbench.action.previousEditor"]}, {"before": ["<C-l>"], "commands": [":bn"]}, {"before": ["<C-h>"], "commands": [":bp"]}, {"before": ["<C-d>"], "commands": [":q"]}, {"before": ["<C-e>"], "commands": ["workbench.explorer.fileView.focus"]}, {"before": ["y", "a"], "after": ["y", "a", "w"]}, {"before": ["d", "j"], "after": ["d", "a", "w"]}, {"before": ["J"], "after": ["5", "j"]}, {"before": ["K"], "after": ["5", "k"]}, {"before": ["L"], "after": ["5", "l"]}, {"before": ["H"], "after": ["5", "h"]}, {"before": ["<leader>"], "after": ["<leader>", "<leader>"]}, {"before": ["f"], "after": ["<leader>", "<leader>", "s"]}, {"before": ["W"], "after": ["<leader>", "<leader>", "<leader>", "j"]}, {"before": ["C", "L", "A"], "commands": ["workbench.action.closeAllEditors"]}, {"before": ["C", "L", "O"], "commands": ["workbench.action.closeOtherEditors"]}, {"before": ["<leader>", "p", "c", "p"], "commands": ["copyRelativeFilePath"]}], "vim.visualModeKeyBindingsNonRecursive": [{"before": ["<space>", "l"], "after": ["$"]}, {"before": ["<space>", "h"], "after": ["^"]}, {"before": ["J"], "after": ["5", "j"]}, {"before": ["K"], "after": ["5", "k"]}, {"before": ["L"], "after": ["5", "l"]}, {"before": ["H"], "after": ["5", "h"]}, {"before": ["p"], "after": ["p", "g", "v", "y"]}, {"before": ["<leader>"], "after": ["<leader>", "<leader>"]}, {"before": ["w"], "after": ["<leader>", "<leader>", "s"]}, {"before": ["W"], "after": ["<leader>", "<leader>", "<leader>", "j"]}], "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[less]": {"editor.defaultFormatter": "biomejs.biome"}, "[html]": {"editor.defaultFormatter": "biomejs.biome"}, "[python]": {"editor.defaultFormatter": "mikoz.black-py"}, "[oraclesql]": {"editor.suggest.showSnippets": true, "editor.quickSuggestions": {"comments": "on", "strings": "on", "other": "on"}}, "git.confirmSync": false, "git.enableSmartCommit": true, "git.autofetch": true, "git.ignoreRebaseWarning": true, "git.openRepositoryInParentFolders": "never", "git.replaceTagsWhenPull": true, "gitlens.advanced.messages": {"suppressCreatePullRequestPrompt": true}, "javascript.updateImportsOnFileMove.enabled": "never", "typescript.updateImportsOnFileMove.enabled": "always", "oracledevtools.connectionConfiguration.configFilesFolder": "/Users/<USER>/Oracle/network/admin", "vscode-edge-devtools.webhintInstallNotification": true, "vscode_custom_css.imports": ["file:///Users/<USER>/.vscode-custom-css/activitybar-icons.css"], "RainbowBrackets.depreciation-notice": false, "catppuccin-noctis-icons.hidesExplorerArrows": false, "symbols.hidesExplorerArrows": false, "cursor.general.gitGraphIndexing": "enabled", "cursor.diffs.useCharacterLevelDiffs": true, "cursor.cmdk.useThemedDiffBackground": true, "git-autoconfig.configList": [{}, {"user.email": "<EMAIL>", "user.name": "DongDong"}], "augment.nextEdit.enableGlobalBackgroundSuggestions": true, "augment.nextEdit.highlightSuggestionsInTheEditor": true, "codeium.enableConfig": {"*": false}}