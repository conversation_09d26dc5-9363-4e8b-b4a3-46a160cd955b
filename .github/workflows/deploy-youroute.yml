name: Deploy YouRoute Worker

# YouRoute Cloudflare Worker 自动部署
# 处理多应用路由分发，支持环境变量管理和多环境部署

on:
  push:
    branches: [main, preview]
    paths:
      - 'devops/workers/youroute/**'
      - '.github/workflows/deploy-youroute.yml'
jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy YouRoute Worker

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Determine deployment environment
        id: env
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "env=production" >> $GITHUB_OUTPUT
            echo "🚀 Deploying to production environment"
          else
            echo "env=preview" >> $GITHUB_OUTPUT
            echo "🔍 Deploying to preview environment"
          fi

      - name: Replace pnpm syntax
        run: |
          node devops/scripts/replace-pnpm-syntax.js

      - name: Deploy routing rules
        run: |
          cd devops/workers/youroute
          npm run deploy:rules:${{ steps.env.outputs.env }}
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ZONE_ID: 638a8cde2deebf90ceaa82f07a98f6a0

      - name: Deploy to Cloudflare Workers
        run: |
          # 安装 wrangler CLI
          npm install -g wrangler@4.25.0

          # 设置认证令牌
          export CLOUDFLARE_API_TOKEN="${{ secrets.CLOUDFLARE_API_TOKEN }}"
          export CLOUDFLARE_ACCOUNT_ID="${{ secrets.CLOUDFLARE_ACCOUNT_ID }}"

          # 切换到工作目录并部署
          cd devops/workers/youroute
          wrangler deploy --env ${{ steps.env.outputs.env }}
