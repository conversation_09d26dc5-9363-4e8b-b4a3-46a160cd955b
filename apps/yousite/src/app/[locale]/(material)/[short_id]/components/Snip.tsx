'use client';
import { SnipArticleVO } from '@repo/common/types/snip/types';
import { ArrowUpRightFromSquare } from 'lucide-react';
import { Link } from '@/i18n/navigation';
import SharedHeader from './header';
// import { useHydrateAtoms } from "jotai/utils";

export function Snip({ snip }: { snip: SnipArticleVO }) {
  // useHydrateAtoms([[snipDetail<PERSON>tom, props.snip]]);
  const { webpage } = snip;
  const url = webpage?.url;

  const linkNode = url ? (
    <Link
      href={url}
      target="_blank"
      className="footnote flex flex-nowrap items-center text-caption"
    >
      <ArrowUpRightFromSquare size={14} className="mr-[2px]" />
      <span className="max-w-[320px] overflow-hidden text-ellipsis whitespace-nowrap">
        {webpage?.site?.host || webpage?.site?.name || url}
      </span>
    </Link>
  ) : null;

  const operationArea = (
    <div>
      {linkNode}
      {/* <SaveMaterial short_id={short_id} hasLoggedIn={!!user} /> */}
    </div>
  );

  return (
    <>
      <SharedHeader extra={operationArea} />
      <main className="mx-auto max-w-[800px] px-6 py-5">
        <div className="snip-content">123</div>
      </main>
    </>
  );
}
