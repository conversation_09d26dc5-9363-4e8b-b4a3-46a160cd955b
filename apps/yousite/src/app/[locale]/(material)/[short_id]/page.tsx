/**
 * 调试时，复制的share链接记得去掉 https 的 s
 */

import { EntityTypeEnum } from '@repo/common/types/entity/enum';
import { type Metadata } from 'next';
import { notFound } from 'next/navigation';

import './style.css';
import { SnipArticleVO } from '@repo/common/types/snip/types';
import { ThoughtVO } from '@repo/common/types/thought/types';
import { Snip } from './components/Snip';
import { Thought } from './components/Thought';

async function getEntity(
  _short_id: string,
): Promise<{ entity_type: EntityTypeEnum; entity: ThoughtVO | SnipArticleVO } | null> {
  return null;
}

export default async function Page({ params: { short_id } }: { params: { short_id: string } }) {
  const result = await getEntity(short_id);
  if (!result) {
    notFound();
  }

  const { entity_type, entity } = result;

  // 根据实体类型渲染不同的组件
  switch (entity_type) {
    case EntityTypeEnum.THOUGHT:
      return <Thought thought={entity as ThoughtVO} />;
    case EntityTypeEnum.SNIP:
      return <Snip snip={entity as SnipArticleVO} />;
    default:
      notFound();
  }
}

export async function generateMetadata({
  params: { short_id },
}: {
  params: { short_id: string };
}): Promise<Metadata> {
  const result = await getEntity(short_id);
  if (!result) {
    notFound();
  }

  const { entity_type, entity } = result;

  let title = entity.title;
  let description = '';

  // 根据实体类型获取不同的元数据
  switch (entity_type) {
    case EntityTypeEnum.THOUGHT: {
      const thought = entity as ThoughtVO;
      title = title ?? 'Untitled Thought';
      description = thought?.content?.plain?.substring?.(0, 100) || '';
      break;
    }
    case EntityTypeEnum.SNIP: {
      const snip = entity as SnipArticleVO;
      title = title ?? 'Untitled Snip';
      description = snip?.content?.plain?.substring?.(0, 100) || '';
      break;
    }
    default:
      return {};
  }

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      url: `https://${process.env.NEXT_PUBLIC_YOUMIND_SITE_HOST}/${short_id}`,
      images: [{ url: '/cover.png' }],
      siteName: 'YouMind',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [{ url: '/cover.png' }],
    },
  };
}
