'use client';

import { BoardWithSomeBoardItemsVO } from '@repo/common/types/board/types';
import { Button } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { toast } from '@repo/ui/components/ui/sonner';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { callHTTP } from '@/lib/request/external';

interface SaveSharedResponse {
  id: string;
  type: 'snip' | 'thought';
  board_ids: string[];
  board_item: {
    board_id: string;
  };
}

export function SaveMaterial(props: { short_id: string; hasLoggedIn: boolean }) {
  const { short_id, hasLoggedIn } = props;
  const router = useRouter();
  const [boards, setBoards] = useState<BoardWithSomeBoardItemsVO[]>([]);
  const [selectedBoardId, setSelectedBoardId] = useState<string>('');
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations();

  useEffect(() => {
    if (hasLoggedIn) {
      callHTTP<BoardWithSomeBoardItemsVO[]>('/api/v1/board/listBoardsWithSomeBoardItems', {
        method: 'POST',
        body: {},
      }).then(({ data }) => {
        if (data) {
          setBoards(data);
          if (data.length > 0) {
            setSelectedBoardId(data?.[0]?.id!);
          }
        }
      });
    }
  }, [hasLoggedIn]);

  const handleSave = async () => {
    if (!hasLoggedIn) {
      router.push('/login');
      return;
    }

    if (!selectedBoardId) {
      toast.error('Please select a board to save to.');
      return;
    }

    const { data, error } = await callHTTP<SaveSharedResponse>('/api/v1/material/saveShared', {
      method: 'POST',
      body: {
        short_id,
        board_id: selectedBoardId,
      },
    });

    if (!error && data) {
      toast.custom((toastId) => (
        <div className="flex items-center justify-between gap-4">
          <div>
            <div className="font-semibold">Saved as yours!</div>
            <div className="text-gray-500">Open to view or customize it.</div>
          </div>
          <Button
            onClick={() => {
              router.push(
                `/boards/${data?.board_item?.board_id}?entity-type=${data.type}&entity-id=${data.id}`,
              );
              toast.dismiss(toastId);
            }}
          >
            {t('Share.open')}
          </Button>
        </div>
      ));
      setIsOpen(false);
    }
  };

  const selectedBoard = boards.find((b) => b.id === selectedBoardId);

  if (!hasLoggedIn) {
    return (
      <Button variant="default" onClick={handleSave}>
        {t('Share.save')}
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            async
            variant="default"
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
            onClick={handleSave}
          >
            {t('Share.save')}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-56"
          onMouseEnter={() => setIsOpen(true)}
          onMouseLeave={() => setIsOpen(false)}
        >
          <DropdownMenuRadioGroup value={selectedBoardId} onValueChange={setSelectedBoardId}>
            {boards.map((board) => (
              <DropdownMenuRadioItem key={board.id} value={board.id!}>
                {board.name}
              </DropdownMenuRadioItem>
            ))}
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
