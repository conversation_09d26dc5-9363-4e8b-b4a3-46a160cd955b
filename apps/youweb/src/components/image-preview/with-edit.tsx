import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { SnipImageVO } from '@repo/common/types/snip/app-types';
import { Button } from '@repo/ui/components/ui/button';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@repo/ui/components/ui/hover-card';
import { toast } from '@repo/ui/components/ui/sonner';
import { Textarea } from '@repo/ui/components/ui/textarea';
import { useAtom, useAtomValue } from 'jotai';
import { ArrowUp, EraserIcon, PencilIcon } from 'lucide-react';
import React from 'react';
import { ImageEditor, type Rectangle } from '@/components/image-preview/editor';
import { panelStateAtom } from '@/hooks/useBoardState';
import { boardDetailAtom, unshiftBoardItemsAtom } from '@/hooks/useBoards';
import { thoughtEditorAtom } from '@/hooks/useThought';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';
import { ImagePreview } from './index';

interface ImagePreviewWithEditProps {
  src: string;
  alt?: string;
  width?: number | string;
  height?: number | string;
  quality?: 'low' | 'medium' | 'high' | 'auto';
  imageList?: string[];
  currentIndex?: number;
  rounded?: boolean;
  blurhash?: string;
  maxWidth?: string;
  className?: string;
  containerClassName?: string;
  isNewBoard?: boolean;
  onImageEdit?: (data: {
    image_url: string;
    original_image_url: string;
    blurhash: string;
    width: number;
    height: number;
    prompt: string;
  }) => Promise<void>;
  saveEnabled?: boolean;
  editEnabled?: boolean;
  insertToThoughtEnabled?: boolean;
}

export const ImagePreviewWithEdit: React.FC<ImagePreviewWithEditProps> = (props) => {
  const thoughtEditor = useAtomValue(thoughtEditorAtom);
  const boardDetail = useAtomValue(boardDetailAtom);
  const [, unshiftBoardItems] = useAtom(unshiftBoardItemsAtom);
  const [isSaving, setIsSaving] = React.useState(false);

  const panelState = useAtomValue(panelStateAtom);
  const thoughtOpened = panelState.panelData?.entity_type === 'thought';

  const handleInsert = () => {
    if (thoughtEditor) {
      try {
        thoughtEditor.commands.insertContentByMarkdown(`![${props.alt || 'image'}](${props.src})`);
      } catch (_err) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      }
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    const boardId = boardDetail?.id;
    try {
      const result = await callHTTP('/api/v1/createImage', {
        method: 'POST',
        body: {
          file: { name: props.alt, original_url: props.src },
          board_id: boardId,
          title: props.alt,
        },
      });

      if (result.error) {
        toast('Failed to save image');
      } else if (result.data) {
        const item = result.data as SnipImageVO;
        unshiftBoardItems([
          {
            ...item.board_item!,
            entity: item,
            entity_type: BoardItemTypeEnum.SNIP,
          },
        ]);
        toast('Saved image');
      }
    } catch (_err) {
      toast('Failed to save image');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEditImage = async (prompt: string, rectangles: Rectangle[], editorWidth: number) => {
    const scale = Number(props.width) / Number(editorWidth);
    const scaledMasks = rectangles.map((rect) => ({
      x: Math.round(rect.x * scale),
      y: Math.round(rect.y * scale),
      width: Math.round(rect.width * scale),
      height: Math.round(rect.height * scale),
    }));

    const result = await callHTTP('/api/v1/editImage', {
      method: 'POST',
      body: {
        url: props.src,
        prompt,
        size: `${props.width}x${props.height}`,
        mask: JSON.stringify(scaledMasks),
        quality: props.quality || 'medium',
      },
    });

    if (result.error) {
      throw new Error('Failed to edit image');
    } else if (result.data) {
      if (props.onImageEdit) {
        await props.onImageEdit({
          ...result.data,
          prompt,
        });
      }
    }
  };

  const createEditRender = () => {
    let editorWidth = 0;

    return ({
      rectangles,
      isErasing,
      isModifying,
      modifyPrompt,
      setModifyPrompt,
      onErase,
      onModify,
      onReset,
      onRectanglesChange,
      src,
      alt,
    }: {
      isEditing: boolean;
      rectangles: Rectangle[];
      isErasing: boolean;
      isModifying: boolean;
      modifyPrompt: string;
      setModifyPrompt: (prompt: string) => void;
      onErase: (e: React.MouseEvent) => void;
      onModify: () => void;
      onReset: (e: React.MouseEvent) => void;
      onRectanglesChange: (rectangles: Rectangle[], width: number, height: number) => void;
      src: string;
      alt: string;
    }) => {
      const handleRectanglesChanged = (rectangles: Rectangle[], width: number, height: number) => {
        editorWidth = width;
        onRectanglesChange(rectangles, width, height);
      };

      const handleErase = async (e: React.MouseEvent) => {
        e.stopPropagation();
        try {
          await handleEditImage('Erase the transparent mask area.', rectangles, editorWidth);
        } catch (_err) {
          toast('Failed to erase image');
        }
        onErase(e);
      };

      const handleModify = async () => {
        try {
          await handleEditImage(
            `Make the following changes in the transparent mask area: ${modifyPrompt}`,
            rectangles,
            editorWidth,
          );
        } catch (_err) {
          toast('Failed to modify image');
        }
        onModify();
      };

      return (
        <>
          <div className="absolute right-[10px] top-[10px] z-[100] flex gap-x-1">
            {rectangles.length > 0 && (
              <>
                <Button
                  className="h-8 rounded-full bg-card hover:bg-card/75"
                  variant="ghost"
                  disabled={isErasing || isModifying}
                  loading={isErasing}
                  onClick={handleErase}
                >
                  {!isErasing && <EraserIcon className="w-4 h-4 mr-2" />}
                  Erase
                </Button>
                <HoverCard openDelay={0}>
                  <HoverCardTrigger asChild>
                    <Button
                      className="h-8 rounded-full bg-card hover:bg-card/75"
                      variant="ghost"
                      disabled={isModifying || isErasing}
                      loading={isModifying}
                    >
                      {!isModifying && <PencilIcon className="w-4 h-4 mr-2" />}
                      Modify
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent className="relative p-2 w-60 rounded-xl">
                    <Textarea
                      className="p-0 bg-transparent border-none"
                      rows={3}
                      placeholder="Describe how you want to modify the contents of the selection."
                      value={modifyPrompt}
                      onChange={(e) => setModifyPrompt(e.target.value)}
                    />
                    <Button
                      className={cn(
                        'absolute bottom-2 right-2 h-6 w-6 rounded-full bg-foreground text-card',
                      )}
                      size="icon"
                      onClick={handleModify}
                      disabled={!modifyPrompt || isModifying}
                    >
                      <ArrowUp size={12} />
                    </Button>
                  </HoverCardContent>
                </HoverCard>
              </>
            )}
            <Button
              className="h-8 rounded-full"
              variant="ghost"
              disabled={isErasing || isModifying}
              onClick={onReset}
            >
              Reset
            </Button>
          </div>
          <ImageEditor src={src} alt={alt} onRectanglesChange={handleRectanglesChanged} />
        </>
      );
    };
  };

  return (
    <ImagePreview
      {...props}
      editRender={createEditRender()}
      isSaving={isSaving}
      onInsert={handleInsert}
      onSave={handleSave}
      insertToThoughtEnabled={thoughtOpened}
    />
  );
};
