import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import type { BoardItemVO } from '@repo/common/types/board/types';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { MessageImageGenerateResult } from '@repo/common/types/chat/enum';
import type { CompletionToolBlock } from '@repo/common/types/chat/types';
import { CompletionBlockStatusEnum } from '@repo/common/types/completion';
import { useSetAtom } from 'jotai';
import { omit } from 'lodash';
import { PencilRuler } from 'lucide-react';
import { NewSnip } from '@/components/icon/new-snip';
import { ImagePreviewWithEdit } from '@/components/image-preview/with-edit';
import { updateChatMessageCompletionBlockAtom } from '@/hooks/ask-ai/useSendChatMessage';
import { focusMaterialByEntityIdAtom } from '@/hooks/useBoardState';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';
import { ToolStatus, ToolTitle } from './card';
import { useAddBoardItemsWhenBlockRunning } from './hooks/useAddSnipResultToBoard';
import { type TOOL_RENDERER, TOOL_SCENE_TYPE } from './type';

const titles = {
  [CompletionBlockStatusEnum.ING]: 'Creating image',
  [CompletionBlockStatusEnum.EXECUTING]: 'Creating image',
  [CompletionBlockStatusEnum.DONE]: 'Created image',
  [CompletionBlockStatusEnum.ERROR]: 'Failed to create image',
};

export const GenerateImageCard = ({
  block,
  variant,
  scene,
}: {
  block: CompletionToolBlock;
  variant: 'small' | 'middle';
  scene: TOOL_SCENE_TYPE;
}) => {
  const focusMaterialByEntityId = useSetAtom(focusMaterialByEntityIdAtom);
  const updateChatMessageCompletionBlock = useSetAtom(updateChatMessageCompletionBlockAtom);
  useAddBoardItemsWhenBlockRunning(block, (block) => {
    const toolResult = block.tool_result as MessageImageGenerateResult;
    const { snip } = toolResult;

    if (snip?.status !== 'success') {
      return [];
    }
    return [
      {
        ...snip.vo?.board_item,
        entity_type: BoardItemTypeEnum.SNIP,
        entity: omit(snip.vo, 'board_item'),
      } as BoardItemVO,
    ];
  });
  const { image_urls, blurhash, width, height, quality, snip, history } =
    (block.tool_result as MessageImageGenerateResult) || {};

  const formattedHistory = history || [];
  if (image_urls && formattedHistory.length === 0) {
    formattedHistory.push({
      image_url: image_urls[0],
      prompt: '',
    });
  }

  // console.log(formattedHistory);

  const handleEdit = async ({
    image_url,
    original_image_url,
    blurhash,
    width,
    height,
    prompt,
  }: {
    image_url: string;
    original_image_url: string;
    blurhash: string;
    width: number;
    height: number;
    prompt: string;
  }) => {
    const newToolResult = {
      ...block.tool_result,
      image_urls: [image_url],
      blurhash,
      width,
      height,
      history: [...(formattedHistory || []), { image_url, prompt }],
    };
    await callHTTP('/api/v1/chat/updateCompletionBlock', {
      method: 'POST',
      body: {
        block_id: block.id,
        tool_result: newToolResult,
      },
    });
    updateChatMessageCompletionBlock({
      message_id: block.message_id,
      block: {
        ...block,
        tool_result: newToolResult,
      },
    });
  };

  return (
    <>
      <ToolTitle
        text={renderTitle(block)}
        icon={snip ? <NewSnip size={14} /> : <PencilRuler size={14} />}
        variant={variant}
        blockStatus={block.status}
        className={cn(
          scene === TOOL_SCENE_TYPE.CHAT &&
            block.tool_result.snip?.status === 'success' &&
            'cursor-pointer',
        )}
        onClick={() => {
          if (scene === TOOL_SCENE_TYPE.CHAT && block.tool_result.snip?.status === 'success') {
            focusMaterialByEntityId(block.tool_result.snip.vo.id);
          }
        }}
      />
      {(image_urls || []).map(
        (url) =>
          url && (
            <ImagePreviewWithEdit
              key={url}
              src={url}
              alt={block.tool_arguments.title}
              rounded={false}
              width={width}
              height={height}
              quality={quality}
              blurhash={blurhash}
              className="rounded-md"
              saveEnabled={
                scene !== TOOL_SCENE_TYPE.CUSTOM_ASSISTANT && scene !== TOOL_SCENE_TYPE.NEW_BOARD
              }
              editEnabled={true}
              onImageEdit={handleEdit}
            />
          ),
      )}
    </>
  );
};

const renderTitle = (block: CompletionToolBlock) => {
  const { snip } = (block.tool_result as MessageImageGenerateResult) || {};
  let toolTitle = titles[block.status] || titles[CompletionBlockStatusEnum.ING];
  if (snip) {
    const { status } = snip;
    if (status === 'processing') {
      toolTitle = 'Creating snip';
    } else if (status === 'success') {
      toolTitle = 'Created snip';
    } else if (status === 'failed') {
      toolTitle = 'Failed to create snip';
    }
  }
  return (
    <>
      <span>{toolTitle}</span>
      {snip?.vo?.title && (
        <span className="ml-2 text-xs text-caption-foreground">{snip?.vo?.title}</span>
      )}
    </>
  );
};

export const GenerateImageTool: TOOL_RENDERER = {
  type: TOOL_TYPES.IMAGE_GENERATE,
  renderer: GenerateImageCard,
  transformToMarkdown: (block) => {
    const { image_urls = [] } =
      (block.tool_result as {
        image_urls: string[];
      }) || {};

    return image_urls.map((url) => `![generated image](${url})`).join('\n');
  },
  getToolTitle: (block) => {
    const { snip } = (block.tool_result as MessageImageGenerateResult) || {};
    return (
      <ToolStatus
        logo={<PencilRuler size={16} />}
        commandName={titles[block.status]}
        commandDescription={snip?.vo?.title}
      />
    );
  },
  needRefreshBoard: true,
};
