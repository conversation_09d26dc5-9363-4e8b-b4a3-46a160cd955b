/**
 * 创建 Chat 命令处理器
 * 处理创建聊天的业务逻辑，包括创建聊天会话和初始消息
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v1/chat/createChat/route.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import { CompletionBlockStatusEnum, ToolCallContext } from '@/common/types';
import { Chat } from '@/modules/chat/domain/chat/models/chat.entity';
import { ChatRepository } from '@/modules/chat/repositories/chat.repository';
import { CallToolCommand } from '../commands/call-tool.command';

@Injectable()
@CommandHandler(CallToolCommand)
export class CallToolHandler implements ICommandHandler<CallToolCommand> {
  private readonly logger = new Logger(CallToolHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly traceService: LangfuseTraceService,
  ) {}

  async execute(command: CallToolCommand) {
    const { subject, generation, toolDefinition, input, completionBlock } = command.param;
    const start = Date.now();

    try {
      let chat: Chat | null = null;
      if (generation.bizArgs?.chatId) {
        chat = await this.chatRepository.findById(generation.bizArgs.chatId);
      }

      await this.traceService.addSpan({
        name: 'call-tool-' + toolDefinition.function.name,
        input: {
          chatId: chat?.id,
          userId: chat?.creatorId || generation.bizArgs.userId,
          params: input,
        },
      });

      const toolCallContext: ToolCallContext = {
        chat,
        userId: chat?.creatorId || generation.bizArgs.userId,
        params: input,
      };

      // Set tool arguments in completion block
      completionBlock.setToolArguments(input);

      // Example of emitting tool progress/status to the stream
      // IMPORTANT: Only use subject.next(), never subject.complete() or subject.error()
      // subject.next({
      //   type: 'tool-call',
      //   toolCallId: completionBlock.toolId,
      //   toolName: toolDefinition.function.name,
      //   args: input,
      // } as any);

      // TODO: Implement actual tool execution
      // const result = await this.toolService.callTool(toolCallContext);

      const result = {
        toolResult: {},
        toolResponse: 'Tool executed successfully',
      };

      // Emit tool result to the stream if needed
      // subject.next({
      //   type: 'tool-result',
      //   toolCallId: completionBlock.toolId,
      //   toolName: toolDefinition.function.name,
      //   result: result.toolResult,
      // } as any);

      // Update completion block with success
      completionBlock.completeExecution(result.toolResult, result.toolResponse, Date.now() - start);
      completionBlock.updateStatus(CompletionBlockStatusEnum.DONE);

      return result.toolResponse;
    } catch (error) {
      // CRITICAL: Handle errors here to maintain database consistency
      // NEVER call subject.error() as it would terminate the entire stream
      this.logger.error(`Tool execution error for ${toolDefinition.function.name}:`, {
        toolCallId: completionBlock.toolId,
        error: error.message,
        stack: error.stack,
      });

      // Update completion block with error status
      completionBlock.updateStatus(CompletionBlockStatusEnum.ERROR);

      // Emit error message to stream instead of terminating it
      // subject.next({
      //   type: 'tool-error',
      //   toolCallId: completionBlock.toolId,
      //   toolName: toolDefinition.function.name,
      //   error: error.message,
      //   timestamp: new Date().toISOString(),
      // } as any);

      // Return error result instead of throwing to prevent stream termination
      return {
        error: true,
        message: error.message || 'Tool execution failed',
        toolName: toolDefinition.function.name,
        toolCallId: completionBlock.toolId,
      };
    }
  }
}
