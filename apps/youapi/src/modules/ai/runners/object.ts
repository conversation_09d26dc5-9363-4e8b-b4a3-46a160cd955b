import { Logger } from '@nestjs/common';
import type { GenerateObjectResult, ObjectStreamPart, TextStreamPart, ToolSet } from 'ai';
import { generateObject, streamObject } from 'ai';
import { merge, Subject } from 'rxjs';
import z, { Zod<PERSON>aw<PERSON>hape, ZodSchema } from 'zod';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '../domain/model/generation';
import { PromptService } from '../prompt/index.service';
import { ModelProviderService } from '../providers/index.service';
import { streamToObservable } from '../utils/toObservable';
import type { GenerateOptions } from './base';
import { BaseRunner } from './base';

/**
 * 生成对象（单次）以及生成对象（多次）
 */
export class ObjectRunner extends BaseRunner {
  protected readonly logger = new Logger(ObjectRunner.name);
  protected schema: ZodSchema;

  setSchema(schema: ZodSchema) {
    this.schema = schema;
    return this;
  }

  public async generateOnce<T>(options?: GenerateOptions): Promise<GenerateObjectResult<T>> {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is not set');
    }
    if (!this.schema) {
      throw new Error('Schema is not set');
    }

    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });

    const traceNode = await this.startTrace(
      model,
      this.currentGeneration.promptName || 'object-generation',
    );

    try {
      const params = this.getCallApiParams(model, new Subject<ObjectStreamPart<ToolSet>>());
      const result = await generateObject({
        ...params,
        output: 'object',
        schema: this.schema,
      });

      const usageReport = this.buildUsageReport(result.usage);

      // Update the traceNode directly instead of using updateGeneration
      traceNode.end({
        ...usageReport,
        output: result.object,
        metadata: {
          finishReason: result.finishReason,
          success: true,
        },
      });

      return result;
    } catch (error) {
      // Update the traceNode directly for error case
      traceNode.end({
        metadata: {
          success: false,
          error: error.message,
          errorStack: error.stack,
        },
        level: 'ERROR',
      });
      throw error;
    }
  }

  public async generateStream(options?: GenerateOptions) {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is not set');
    }
    if (!this.schema) {
      throw new Error('Schema is not set');
    }

    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });
    const traceNode = await this.startTrace(
      model,
      this.currentGeneration.promptName || 'stream-object-generation',
    );

    const toolResponseSubject = new Subject<TextStreamPart<ToolSet>>();
    const params = this.getCallApiParams(model, toolResponseSubject);

    const generator = await streamObject({
      ...params,
      output: 'object',
      schema: this.schema,
      onFinish: ({ usage, object }) => {
        toolResponseSubject.complete();

        const usageReport = this.buildUsageReport(usage);
        traceNode.end({
          ...usageReport,
          output: object,
          metadata: {
            success: true,
          },
        });
      },
      onError: ({ error }) => {
        toolResponseSubject.complete();

        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    // 合并 toolResponseSubject 与 generator，作为最后的消息流返回
    const mergedObservable = merge(
      toolResponseSubject.asObservable(),
      streamToObservable(generator),
    );
    return mergedObservable;
  }

  static fromPrompt(
    promptName: string,
    variables: Record<string, any>,
    schema?: ZodRawShape | ZodSchema, // JSON Schema object
  ) {
    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages(promptName, variables);
    const generation = new Generation({
      prompt,
      promptMessages,
    });
    const runner = new ObjectRunner();
    if (schema) {
      runner.setSchema(schema instanceof ZodSchema ? schema : z.object(schema));
    } else if ((prompt.config as { response_format?: ZodRawShape }).response_format) {
      runner.setSchema(
        z.object((prompt.config as { response_format?: ZodRawShape }).response_format),
      );
    }
    runner.addGeneration(generation);
    return runner;
  }
}
