'use client';

import { marked } from 'marked';
import { useEffect, useRef, useState } from 'react';

import { useWatchTrackActions } from '../hooks';
import styles from './summary-view.module.css';

const ANCHOR_CLASS = 'ym-citation-trigger';

export function convertTimestampToSeconds(timestamp: string) {
  if (!timestamp) return 0;

  const parts = timestamp.split(':').map(Number);
  const minutes = parts[0] || 0;
  const seconds = parts[1] || 0;
  return minutes * 60 + seconds;
}

interface SummaryViewProps {
  content: string;
  videoId: string;
  pointBackground?: string;
}

// 颜色索引映射
const colorIndexMap: Record<number, string> = {
  1: 'dark-blue',
  2: 'blue',
  3: 'purple',
  4: 'green',
  5: 'red',
  0: 'yellow',
};

// 全局变量用于跟踪标题索引
let h3history: Record<number, { animated: boolean; color: string }> = {};
let h4history: Record<number, { animated: boolean }> = {};
let h3IndexCounter = 0;
let h4IndexCounter = 0;

const refreshCounter = () => {
  h3IndexCounter = 0;
  h4IndexCounter = 0;
};

const refreshHistory = () => {
  refreshCounter();
  h3history = {};
  h4history = {};
};

// 配置 marked 渲染器
marked.use({
  renderer: {
    // @ts-expect-error 预期内
    link(_href, _title, text) {
      return text;
    },
    // @ts-expect-error 预期内
    heading(text, level) {
      if (level === 3) {
        h3IndexCounter += 1;
        // 拿到当前的颜色
        let color = '';
        const curH3History = h3history[h3IndexCounter];
        const shouldAnimate = !curH3History?.animated;
        if (curH3History) {
          color = curH3History.color;
        } else {
          color = colorIndexMap[h3IndexCounter % Object.keys(colorIndexMap).length] || 'yellow';
          h3history[h3IndexCounter] = { animated: true, color };
        }
        // 如果当前 h3 是最后一个
        const isLastH3 = h3IndexCounter === Object.keys(h3history).length;
        const pointElement = `<div class="${shouldAnimate && styles['animated-point']} ${styles.point} top-[-2px] left-[-37px]"><button anchor-action="ov-h3-${h3IndexCounter}"></button></div>`;
        return `<div class="${styles[color]} relative">
        <div class="${styles.vline} ${isLastH3 && styles.lastline}"></div>
        <h3 class="relative ml-[31px]" anchor="ov-h3-${h3IndexCounter}" index="${h3IndexCounter}">${text} ${pointElement}</h3>`;
      }

      if (level === 4) {
        h4IndexCounter += 1;
        const shouldAnimate = !h4history[h4IndexCounter]?.animated;
        h4history[h4IndexCounter] = { animated: true };
        const arcElement = `<div anchor="ov-h4-${h4IndexCounter}" class="${shouldAnimate && styles['animated-arc']} ${styles.arc} top-[-19px] left-[-43px] "/>`;
        const pointElement = `<div class="${shouldAnimate && styles['animated-point']} ${styles.point} top-[17px] left-[17px]"><button anchor-action="ov-h4-${h4IndexCounter}" anchor="ov-h4-${h4IndexCounter}"></button></div>`;
        return `<h4 h3="${h3IndexCounter}" index="${h4IndexCounter}" class="relative ml-12" anchor="ov-h4-${h4IndexCounter}">${text} ${arcElement} ${pointElement}</h4>`;
      }
      return `<h${level}>${text}</h${level}>`;
    },
    list(token: any) {
      return `<ul class="ml-8" h3="${h3IndexCounter}" h4="${h4IndexCounter}">${token}</ul>`;
    },
    listitem(token: any) {
      // 删除锚点后面的内容
      const text = token.text.replace(/(\[citation-[^\]]+\]).*/, '$1');
      const citation = token.text.match(/\[citation-([^\]]+)\]/)?.[1];
      const hasCitation = !!citation;
      return `<li data-citation="${citation}" class="text-secondary-foreground ${styles.li} ${hasCitation && ' cursor-pointer'} ${hasCitation && ANCHOR_CLASS}">${text}</li>`;
    },
    paragraph(text: any) {
      return `<p>${text}</p>`;
    },
  },
});

export function SummaryView({ content, videoId, pointBackground }: SummaryViewProps) {
  const [html, setHtml] = useState('');
  const [hiddenH3Map, setHiddenH3Map] = useState<Record<string, boolean>>({});
  const [hiddenH4Map, setHiddenH4Map] = useState<Record<string, boolean>>({});
  const contentRef = useRef<HTMLDivElement>(null);
  const { trackTimestampClick } = useWatchTrackActions();

  // 刷新全局变量
  useEffect(() => {
    refreshHistory();
  }, []);

  // 将 Markdown 转换为 HTML
  const summarizeToHtml = async (markdown: string) => {
    refreshCounter();
    const output = await marked(markdown);
    const formattedHtml = output.replace(
      /\[citation-([^\]]+)\]/g,
      `<button data-citation="$1" style="display:none" class="${ANCHOR_CLASS} ym-citation-btn ${styles.anchor}"><svg class="pointer-events-none" width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.645277 3.07963L7.18503 0.787384C7.2285 0.772127 7.27545 0.769787 7.32022 0.780645C7.36499 0.791503 7.40566 0.815095 7.43731 0.84857C7.46896 0.882044 7.49024 0.923969 7.49857 0.969277C7.50691 1.01458 7.50194 1.06134 7.48428 1.10388L4.99678 7.09838C4.91253 7.30238 4.62028 7.29213 4.54978 7.08263L3.66553 4.44988C3.65279 4.4126 3.63112 4.379 3.6024 4.35202C3.57368 4.32504 3.53879 4.30551 3.50078 4.29513L0.662527 3.53488C0.435277 3.47463 0.423527 3.15688 0.645277 3.07938V3.07963Z" fill="currentColor" fill-opacity="0.5"/>
</svg></button>`,
    );

    // 添加关闭的 div 标签
    const finalHtml = `${formattedHtml}</div>`;
    setHtml(finalHtml);
  };

  // 处理引用点击事件
  const handleCitationClick = async (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target?.classList.contains(ANCHOR_CLASS)) return;

    e.preventDefault();
    const citation = target.getAttribute('data-citation');

    if (citation) {
      try {
        const seconds = convertTimestampToSeconds(citation);
        // 触发视频跳转事件
        const event = new CustomEvent('video-seek', {
          detail: { seconds, videoId },
        });
        window.dispatchEvent(event);

        // 埋点追踪
        trackTimestampClick(citation, videoId);
      } catch (_error) {
        console.error('Invalid timestamp format:', citation);
      }
    }
  };

  // 处理标题点击事件（折叠/展开）
  const handlePointClick = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target?.getAttribute('anchor-action')) {
      e.preventDefault();
      const anchor = target.getAttribute('anchor-action');

      // 处理 h3 点击
      if (anchor?.startsWith('ov-h3')) {
        const currentH3Index = document
          .querySelector(`h3[anchor="${anchor}"]`)
          ?.getAttribute('index');
        if (!currentH3Index) return;

        setHiddenH3Map((prev) => ({
          ...prev,
          [currentH3Index]: !prev[currentH3Index],
        }));
      }

      // 处理 h4 点击
      if (anchor?.startsWith('ov-h4')) {
        const currentH4Index = document
          .querySelector(`h4[anchor="${anchor}"]`)
          ?.getAttribute('index');
        if (!currentH4Index) return;

        setHiddenH4Map((prev) => {
          const nextStatus = !prev[currentH4Index];
          return {
            ...prev,
            [currentH4Index]: nextStatus,
          };
        });
      }
    }
  };

  // 监听引用点击事件
  useEffect(() => {
    window.addEventListener('click', handleCitationClick);
    return () => {
      window.removeEventListener('click', handleCitationClick);
    };
  }, [videoId]);

  // 监听标题点击事件（折叠/展开）
  useEffect(() => {
    window.addEventListener('click', handlePointClick);

    // 清理函数
    return () => {
      window.removeEventListener('click', handlePointClick);
    };
  }, []); // 添加依赖项

  // 处理内容变化
  useEffect(() => {
    if (content) {
      summarizeToHtml(content);
    }
  }, [content]);

  // 加载完成后，处理 hover 事件
  useEffect(() => {
    if (!content) {
      return;
    }
    setTimeout(() => {
      // 监听所有 li 的 hover 事件，然后修改 li 内的 button 的 style
      const allLi = document.querySelectorAll('li');
      allLi.forEach((li) => {
        const button = li.querySelector('button');
        if (!button) return;
        li.addEventListener('mouseover', () => {
          button.style.display = 'inline-flex';
        });
        li.addEventListener('mouseleave', () => {
          button.style.display = 'none';
        });
      });
    }, 200);
  }, [content]);

  // 处理折叠/展开状态
  useEffect(() => {
    try {
      Object.keys(hiddenH4Map).forEach((key) => {
        // 隐藏对应的 dom
        const childs = document.querySelectorAll(`[h4="${key}"]`);
        if (childs.length) {
          childs.forEach((child) => {
            if (hiddenH4Map[key]) {
              if (styles.hiddenByH3) child.classList.add(styles.hiddenByH3);
            } else {
              if (styles.hiddenByH3) child.classList.remove(styles.hiddenByH3);
            }
          });
        }
        // 改变对应的 point 的样式
        const point = document.querySelector(`[anchor-action="ov-h4-${key}"]`);
        if (hiddenH4Map[key]) {
          if (styles['point-collapse']) point?.classList.add(styles['point-collapse']);
        } else {
          if (styles['point-collapse']) point?.classList.remove(styles['point-collapse']);
        }
      });
      Object.keys(hiddenH3Map).forEach((key) => {
        const childs = document.querySelectorAll(`[h3="${key}"]`);
        if (childs.length) {
          childs.forEach((child) => {
            if (hiddenH3Map[key]) {
              if (styles.hiddenByH4) child.classList.add(styles.hiddenByH4);
            } else {
              if (styles.hiddenByH4) child.classList.remove(styles.hiddenByH4);
            }
          });
        }
        // 改变对应的 point 的样式
        const point = document.querySelector(`[anchor-action="ov-h3-${key}"]`);
        if (hiddenH3Map[key]) {
          if (styles['point-collapse']) point?.classList.add(styles['point-collapse']);
        } else {
          if (styles['point-collapse']) point?.classList.remove(styles['point-collapse']);
        }
      });
    } catch (error) {
      console.warn('hiding tree error', error);
    }
  }, [html, hiddenH3Map, hiddenH4Map]);

  if (!content) {
    return (
      <div className="flex flex-col items-center py-8 text-center">
        <div className="text-gray-500">No summary available</div>
      </div>
    );
  }

  return (
    <div
      className="overview-container"
      style={{ '--point-background': pointBackground } as React.CSSProperties}
    >
      <div
        ref={contentRef}
        className="overview-container body"
        dir="auto"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: 需要渲染 markdown 内容
        dangerouslySetInnerHTML={{ __html: html }}
      />
    </div>
  );
}
