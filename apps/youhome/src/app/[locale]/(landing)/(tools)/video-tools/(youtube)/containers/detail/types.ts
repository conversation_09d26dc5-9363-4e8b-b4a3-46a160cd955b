// === 主应用数据结构 ===

// 内容项 (对应主应用的content结构)
export interface Contents {
  block_id: string; // 块ID
  format: 'llm-output' | 'subtitle-formatted' | 'subtitle'; // 格式
  id: string; // 内容ID
  language: string;
  plain: string; // 纯文本格式
  raw: string; // JSON格式数据
  status: string; // 状态
  trace_id?: string; // 跟踪ID
}

export interface authorItem {
  name: string;
  picture: string;
}

export interface WatchVideoData {
  id: string;
  title: string;
  author: authorItem[];
  views: string;
  published_at?: string;
  created_at: string;
  // 这里没有updated_at
  hero_image_url: string;
  play_url: string;
  status?: string; // "fetching" 表示内容正在生成中，字段消失表示生成完成

  // 概述数据 - 支持静态格式和主应用格式
  overview: {
    contents: Contents[]; // content[0], content[1] 等实际数据
    current_content_id: string; // 当前激活的内容ID
    type: 'overview' | 'transcript';
  };

  // 转录数据 - 支持静态格式和主应用格式
  transcript: {
    contents: Contents[]; // content[0], content[1] 等实际数据
    current_content_id: string; // 当前激活的内容ID
    type: 'overview' | 'transcript';
  };
}
