import Image from 'next/image';

import { YouTubePageConfig } from '../../config';

interface UserGuideProps {
  config: YouTubePageConfig;
  className?: string;
}

export default function UserGuide({ config, className = '' }: UserGuideProps) {
  return (
    <div className={`mb-[100px] ${className}`}>
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-3xl md:text-4xl">{config.userGuide.title}</h2>
      </div>

      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          {/* Left side - Steps */}
          <div className="space-y-8">
            {config.userGuide.steps.map((step) => (
              <div key={step.number} className="flex items-start gap-4">
                <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-black text-sm font-bold text-white">
                  {step.number}
                </div>
                <div>
                  <h3 className="mb-2 text-xl font-semibold">{step.title}</h3>
                  <p className="leading-relaxed text-[#999999]">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Right side - Demo Screenshot */}
          <div className="relative">
            <div className="rounded-lg bg-gray-100 p-4 shadow-lg">
              <Image
                src="/demo/youtube-transcript-demo.png"
                alt="YouTube Transcript Generator Demo"
                width={600}
                height={400}
                className="h-auto w-full rounded"
                priority={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
