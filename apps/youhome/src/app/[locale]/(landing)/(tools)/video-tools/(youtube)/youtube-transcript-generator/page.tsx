import { Metadata } from 'next';

import YouTubePageTemplate from '../containers/intro/YouTubePageTemplate';
import { transcriptConfig } from './config';

export default async function YouTubeTranscriptGeneratorPage() {
  return <YouTubePageTemplate config={transcriptConfig} enableCredits={true} />;
}

export const metadata: Metadata = {
  title: transcriptConfig.metaTitle,
  robots: 'index,follow',
  alternates: {
    canonical: transcriptConfig.canonical,
  },
  other: {
    googlebot: 'index,follow',
  },
};
