'use client';

import { use<PERSON>tom } from 'jotai';
import { watchDisplayLineByLineAtom } from '../atoms';
import { WatchTranscriptItem } from '../hooks';
import { Contents } from '../types';
import { ChapterTranscript } from './ChapterTranscript';
import { LineByLineTranscript } from './LineByLineTranscript';
import { SummaryView } from './SummaryView';

interface ContentRendererProps {
  content: Contents;
  transcriptItems?: WatchTranscriptItem[]; // 保持兼容性：用于 subtitle 格式
  subtitleTranscript?: WatchTranscriptItem[]; // 新增：subtitle格式的转录内容（用于LineView）
  subtitleFormattedTranscript?: WatchTranscriptItem[]; // 新增：subtitle-formatted格式的转录内容（用于ChapterView）
  currentSubtitleContent?: Contents | null; // 新增：subtitle格式的内容对象
  currentSubtitleFormattedContent?: Contents | null; // 新增：subtitle-formatted格式的内容对象
  videoId: string;
}

export function ContentRenderer({
  content,
  transcriptItems = [],
  subtitleTranscript = [],
  subtitleFormattedTranscript = [],
  currentSubtitleContent,
  currentSubtitleFormattedContent,
  videoId,
}: ContentRendererProps) {
  const [displayLineByLine] = useAtom(watchDisplayLineByLineAtom);

  // 根据视图模式和数据格式决定渲染逻辑
  if (currentSubtitleContent || currentSubtitleFormattedContent) {
    // 如果有分别的subtitle和subtitle-formatted数据，则根据视图模式选择
    if (displayLineByLine) {
      // LineView：使用subtitle格式数据
      if (subtitleTranscript.length > 0) {
        return <LineByLineTranscript transcript={subtitleTranscript} videoId={videoId} />;
      }
      // 降级：如果没有subtitle数据，使用subtitle-formatted数据
      return <LineByLineTranscript transcript={subtitleFormattedTranscript} videoId={videoId} />;
    } else {
      // ChapterView：使用subtitle-formatted格式数据
      if (subtitleFormattedTranscript.length > 0) {
        return <ChapterTranscript transcript={subtitleFormattedTranscript} videoId={videoId} />;
      }
      // 降级：如果没有subtitle-formatted数据，使用subtitle数据
      return <ChapterTranscript transcript={subtitleTranscript} videoId={videoId} />;
    }
  }

  // 保持兼容性：使用原有逻辑
  switch (content.format) {
    case 'subtitle':
    case 'subtitle-formatted':
      // 转录格式：允许用户在 Line View 和 Chapter View 之间切换
      return displayLineByLine ? (
        <LineByLineTranscript transcript={transcriptItems} videoId={videoId} />
      ) : (
        <ChapterTranscript transcript={transcriptItems} videoId={videoId} />
      );

    case 'llm-output':
      // llm-output 格式：固定使用 SummaryView
      return <SummaryView content={content.plain} videoId={videoId} />;

    default:
      // 未知格式的降级处理
      return (
        <div className="flex flex-col items-center py-8 text-center">
          <div className="text-gray-500">Unsupported content format: {content.format}</div>
        </div>
      );
  }
}
