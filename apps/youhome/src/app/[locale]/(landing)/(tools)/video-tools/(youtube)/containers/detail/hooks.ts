import { use<PERSON>tom } from 'jotai';
import type { MouseEvent } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { watchDisplayLineByLineAtom } from './atoms';
import { Contents, WatchVideoData } from './types';

// 转录项接口定义
export interface WatchTranscriptItem {
  timestamp: string;
  content: string;
  speaker?: string;
}

// 复用主应用的导出逻辑，适配 watch 页面的数据格式
export function useWatchTranscriptExport() {
  const [displayLineByLine] = useAtom(watchDisplayLineByLineAtom);

  const exportTranscript = useCallback((content: string, videoId: string) => {
    const fileName = `youtube-transcript-${videoId}.txt`;
    const link: HTMLAnchorElement = document.createElement('a');

    if (window.Blob && window.URL) {
      const blobObj = new Blob([content], { type: 'text/plain;charset=utf-8' });

      // 兼容 IE 浏览器
      // @ts-expect-error - msSaveBlob is not in the types
      if (navigator.msSaveBlob) {
        // @ts-expect-error - msSaveBlob is not in the types
        navigator.msSaveBlob(blobObj, fileName);
      } else {
        link.addEventListener('click', () => {
          link.download = fileName;
          link.href = window.URL.createObjectURL(blobObj);
        });
      }
    }

    // 触发下载
    if (link.click) {
      link.click();
    } else {
      const e = document.createEvent('MouseEvents');
      e.initEvent('click', false, false);
      link.dispatchEvent(e);
    }
  }, []);

  const handleExport = useCallback(
    (transcript: WatchTranscriptItem[], videoId: string, overrideDisplayLineByLine?: boolean) => {
      const useLineByLine =
        overrideDisplayLineByLine !== undefined ? overrideDisplayLineByLine : displayLineByLine;

      if (!transcript || transcript.length === 0) {
        console.warn('No transcript content to export');
        return;
      }

      if (useLineByLine) {
        // Line 模式：时间戳 + 内容
        const content = transcript
          .map(({ timestamp, content, speaker }) => {
            const speakerPrefix = speaker ? `[${speaker}] ` : '';
            return `${timestamp} ${speakerPrefix}${content}`;
          })
          .join('\n');
        exportTranscript(content, videoId);
      } else {
        // Chapter 模式：按说话人分组
        const groupedContent = groupTranscriptBySpeaker(transcript);
        const content = groupedContent
          .map(({ speaker, content }) => {
            const timestamp = content[0]?.timestamp || '';
            const speakerHeader = speaker ? `${speaker} ${timestamp}` : timestamp;
            const textContent = content.map(({ text }) => text).join(' ');
            return `${speakerHeader}\n${textContent}`;
          })
          .join('\n\n');
        exportTranscript(content, videoId);
      }
    },
    [displayLineByLine, exportTranscript],
  );

  return {
    handleExport,
  };
}

// 按说话人分组转录内容的辅助函数
function groupTranscriptBySpeaker(transcript: WatchTranscriptItem[]) {
  const grouped: Array<{
    speaker: string;
    content: Array<{ timestamp: string; text: string }>;
  }> = [];

  transcript.forEach((item) => {
    const speaker = item.speaker || 'Speaker';
    const lastGroup = grouped[grouped.length - 1];

    if (lastGroup && lastGroup.speaker === speaker) {
      // 同一个说话人，添加到现有组
      lastGroup.content.push({
        timestamp: item.timestamp,
        text: item.content,
      });
    } else {
      // 新的说话人，创建新组
      grouped.push({
        speaker,
        content: [
          {
            timestamp: item.timestamp,
            text: item.content,
          },
        ],
      });
    }
  });

  return grouped;
}

// 获取当前激活的内容
function getCurrentContent(
  contentData: WatchVideoData['transcript'] | WatchVideoData['overview'],
): Contents | undefined {
  return contentData.contents.find((content) => content.id === contentData.current_content_id);
}

/**
 * 视频转录格式处理 Hook
 *
 * 业务逻辑说明：
 * - 每个 YouTube 视频基于其原始语言生成转录（英语视频→英语转录，中文视频→中文转录）
 * - 后端返回同一语言的多种格式内容，而非多语言内容
 * - 本 Hook 专注于格式选择和内容处理，而非语言切换
 *
 * 支持的内容格式：
 * - subtitle: 原始时间戳转录（用于 Line View）
 * - subtitle-formatted: 格式化章节转录（用于 Chapter View）
 * - llm-output: AI 生成的概述内容
 */
export function useWatchTranscriptFormats(videoData: WatchVideoData | null) {
  // 获取 subtitle 格式内容（原始转录，包含时间戳）
  const subtitleContent = useMemo(() => {
    if (!videoData?.transcript) return null;

    return (
      videoData.transcript.contents.find((content: Contents) => content.format === 'subtitle') ||
      null
    );
  }, [videoData?.transcript]);

  // 获取 subtitle-formatted 格式内容（格式化转录，章节形式）
  const subtitleFormattedContent = useMemo(() => {
    if (!videoData?.transcript) return null;

    return (
      videoData.transcript.contents.find(
        (content: Contents) => content.format === 'subtitle-formatted',
      ) || null
    );
  }, [videoData?.transcript]);

  // 解析 subtitle 格式的转录内容（用于 Line View）
  const subtitleTranscript = useMemo(() => {
    if (!subtitleContent?.plain) return [];
    return parseTranscriptContent(subtitleContent.plain);
  }, [subtitleContent]);

  // 解析 subtitle-formatted 格式的转录内容（用于 Chapter View）
  const subtitleFormattedTranscript = useMemo(() => {
    if (!subtitleFormattedContent?.plain) return [];
    return parseTranscriptContent(subtitleFormattedContent.plain);
  }, [subtitleFormattedContent]);

  // 智能选择最佳显示格式
  // 优先使用格式化版本，回退到原始版本
  const currentTranscript = useMemo(() => {
    return subtitleFormattedTranscript.length > 0
      ? subtitleFormattedTranscript
      : subtitleTranscript;
  }, [subtitleFormattedTranscript, subtitleTranscript]);

  // 获取当前最佳内容对象
  // 用于向后兼容和获取完整的 Contents 信息
  const currentContent = useMemo(() => {
    if (!videoData?.transcript) return null;

    // 优先返回格式化内容，回退到原始内容
    return subtitleFormattedContent || subtitleContent;
  }, [subtitleFormattedContent, subtitleContent]);

  // 检测可用的转录格式
  const availableFormats = useMemo(() => {
    const formats = [];
    if (subtitleContent) {
      formats.push({ type: 'subtitle', name: 'Line View', content: subtitleContent });
    }
    if (subtitleFormattedContent) {
      formats.push({
        type: 'subtitle-formatted',
        name: 'Chapter View',
        content: subtitleFormattedContent,
      });
    }
    return formats;
  }, [subtitleContent, subtitleFormattedContent]);

  // 获取视频的主要语言（用于显示和SEO）
  const videoLanguage = useMemo(() => {
    const language = currentContent?.language || 'en';
    const languageName = getLanguageName(language);
    return {
      code: language as string, // 确保类型为 string
      name: languageName || language.toUpperCase(), // 确保有回退值
    };
  }, [currentContent]);

  return {
    // 主要内容
    currentContent, // 当前最佳内容对象（向后兼容）
    currentTranscript, // 当前最佳解析后的转录内容（向后兼容）

    // 分格式内容
    subtitleContent, // subtitle 格式的原始内容对象
    subtitleFormattedContent, // subtitle-formatted 格式的原始内容对象
    subtitleTranscript, // subtitle 格式的解析内容（Line View）
    subtitleFormattedTranscript, // subtitle-formatted 格式的解析内容（Chapter View）

    // 格式信息
    availableFormats, // 可用的格式列表
    hasMultipleFormats: availableFormats.length > 1, // 是否有多种格式可选

    // 语言信息（单一语言）
    videoLanguage, // 视频的主要语言信息

    // 向后兼容字段（避免破坏现有组件）
    selectedLanguage: videoLanguage.code, // 兼容：当前语言代码
    availableLanguages: [videoLanguage], // 兼容：可用语言列表（永远只有一个）
    isMultiLanguage: false, // 兼容：多语言标识（永远为 false）
    setSelectedLanguage: () => {}, // 兼容：空函数，无实际作用
  };
}

/**
 * 向后兼容的别名
 * @deprecated 建议使用 useWatchTranscriptFormats，该别名仅为向后兼容保留
 */
export const useWatchMultiLanguageTranscript = useWatchTranscriptFormats;

// 辅助函数：解析转录内容
function parseTranscriptContent(plain: string): WatchTranscriptItem[] {
  const lines = plain.split('\n').filter((line) => line.trim());
  const contents: WatchTranscriptItem[] = [];

  for (const line of lines) {
    // 匹配时间戳格式：0:00: content 或 [content](#00:00)
    const timeMatch1 = line.match(/^(\d+:\d+):\s*(.+)$/);
    const timeMatch2 = line.match(/^\[(.+?)\]\(#(\d+:\d+)\)$/);

    if (timeMatch1?.[1] && timeMatch1[2]) {
      contents.push({
        timestamp: timeMatch1[1],
        content: timeMatch1[2].replace(/^-\s*/, ''), // 移除开头的短横线
      });
    } else if (timeMatch2?.[1] && timeMatch2[2]) {
      contents.push({
        timestamp: timeMatch2[2],
        content: timeMatch2[1],
      });
    }
  }

  return contents;
}

// 语言代码到语言名称的映射
function getLanguageName(languageCode: string): string {
  const languageMap: Record<string, string> = {
    en: 'English',
    zh: 'Chinese',
    'zh-CN': 'Chinese (Simplified)',
    'zh-TW': 'Chinese (Traditional)',
    ja: 'Japanese',
    ko: 'Korean',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    it: 'Italian',
    pt: 'Portuguese',
    ru: 'Russian',
    ar: 'Arabic',
    hi: 'Hindi',
    th: 'Thai',
    vi: 'Vietnamese',
  };

  return languageMap[languageCode] || languageCode.toUpperCase();
}

// 复用主应用的埋点追踪功能
export function useWatchTrackActions() {
  const trackViewModeChange = useCallback((mode: 'line' | 'chapter', videoId: string) => {
    // TODO: 实现埋点上报
    console.log('Track view mode change:', { mode, videoId });
  }, []);

  const trackTimestampClick = useCallback((timestamp: string, videoId: string) => {
    // TODO: 实现埋点上报
    console.log('Track timestamp click:', { timestamp, videoId });
  }, []);

  const trackTranscriptAction = useCallback(
    (action: 'copy' | 'download' | 'export', videoId: string) => {
      // TODO: 实现埋点上报
      console.log('Track transcript action:', { action, videoId });
    },
    [],
  );

  const trackTabChange = useCallback((tab: string, videoId: string) => {
    // TODO: 实现埋点上报
    console.log('Track tab change:', { tab, videoId });
  }, []);

  const trackLanguageChange = useCallback((language: string, videoId: string) => {
    // TODO: 实现埋点上报
    console.log('Track language change:', { language, videoId });
  }, []);

  return {
    trackViewModeChange,
    trackTimestampClick,
    trackTranscriptAction,
    trackTabChange,
    trackLanguageChange,
  };
}

// 概述内容处理 hook
export function useWatchOverview(videoData: WatchVideoData | null) {
  // 获取概述内容（通常是 llm-output 格式）
  const overviewContent = useMemo(() => {
    if (!videoData?.overview) return null;

    // 获取当前激活的概述内容
    return getCurrentContent(videoData.overview);
  }, [videoData?.overview]);

  return {
    overviewContent,
  };
}

// 鼠标拖拽检测 hook - 避免误触点击事件
export function useDragDetection() {
  const [isDragging, setIsDragging] = useState(false);
  const [initialPosition, setInitialPosition] = useState({ x: 0, y: 0 });

  const handleMouseDown = useCallback((e: MouseEvent) => {
    setInitialPosition({ x: e.clientX, y: e.clientY });
    setIsDragging(false);
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) {
        const distanceX = Math.abs(e.clientX - initialPosition.x);
        const distanceY = Math.abs(e.clientY - initialPosition.y);
        const threshold = 5;
        if (distanceX > threshold || distanceY > threshold) {
          setIsDragging(true);
        }
      }
    },
    [isDragging, initialPosition],
  );

  const handleMouseUp = useCallback(
    (callback: () => void) => {
      if (!isDragging) {
        callback();
      }
    },
    [isDragging],
  );

  return {
    isDragging,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
  };
}
