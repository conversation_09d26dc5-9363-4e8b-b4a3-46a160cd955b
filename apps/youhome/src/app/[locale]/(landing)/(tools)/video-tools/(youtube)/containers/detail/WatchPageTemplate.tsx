// WatchPageTemplate: 统一的 YouTube 视频播放页模板
// * === 数据流设计 ===
//  *
//  * 1. 用户通过URL访问视频页面 (不再使用videoId)
//  * 2. 检查是否为预设视频 → 返回静态数据 (通过extractVideoId获取videoId判断)
//  * 3. 调用 /api/v1/snip/getSnipByUrl 检查数据库缓存
//  * 4. 如果返回null，调用 /api/v1/tryCreateSnipByUrl 创建新资源
//  * 5. 根据后端情况决定是否需要使用适配器将返回数据统一转换为 WatchVideoData 格式，还是直接拿到WatchVideoData 格式
//  * 6. 如果API失败，降级使用mock数据保证用户体验

'use client';

import { Alert, AlertDescription } from '@repo/ui/components/ui/alert';
import { Button } from '@repo/ui/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/ui/card';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getPresetVideo, isPresetVideo } from '../../preset-videos';
import { WatchPageContainer } from './components/WatchPageContainer';
import { generateMockVideoData } from './mock-data';
import { WatchVideoData } from './types';
import { extractVideoId } from './utils';

interface WatchPageTemplateProps {
  url: string;
  locale: string;
  pageType?: 'transcript' | 'summary'; // 新增：页面类型，用于设置默认tab
}

export default function WatchPageTemplate({
  url,
  locale,
  pageType = 'transcript', // 默认为transcript
}: WatchPageTemplateProps) {
  // const t = useTranslations('Credits');
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isPolling, setIsPolling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videoData, setVideoData] = useState<WatchVideoData | null>(null);

  useEffect(() => {
    generateTranscript();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url]);

  const generateTranscript = async () => {
    setIsLoading(true);
    setIsPolling(false);
    setError(null);
    try {
      const videoId = extractVideoId(url);
      // 优先判断是否是预设视频
      if (isPresetVideo(videoId)) {
        await new Promise((resolve) => setTimeout(resolve, 500));
        const presetData = getPresetVideo(videoId);
        if (presetData) {
          setVideoData(presetData);
          return;
        }
      }
      setIsPolling(true);
      setIsLoading(false);
      // const data = await /api/v1/tryCreateSnipByUrl(url); 实际获取数据的API，等后端实现再修改
      const data = null; // 暂时使用mock数据
      if (data) {
        setVideoData(data);
      } else {
        const mockData = generateMockVideoData(videoId, url);
        setVideoData(mockData);
      }
    } catch (_err) {
      const videoId = extractVideoId(url);
      const mockData = generateMockVideoData(videoId, url);
      setVideoData(mockData);
      setError('Using demo data due to API issues.');
    } finally {
      setIsLoading(false);
      setIsPolling(false);
    }
  };

  const handleBack = () => router.back();
  const handleRetry = () => generateTranscript();

  if (isLoading || isPolling) {
    return (
      <div className="min-h-screen bg-white">
        <div className="sticky top-0 z-10 border-b border-gray-200 bg-white">
          <div className="container py-4">
            <Button
              variant="ghost"
              onClick={handleBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="container py-8">
          <div className="mx-auto max-w-2xl">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  {isPolling ? 'Processing Video...' : 'Loading...'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4 text-gray-600">
                  {isPolling
                    ? "The video is being processed. We're generating transcript and AI analysis. This may take 1-3 minutes."
                    : 'Checking if video data is available...'}
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span
                      className={`h-2 w-2 rounded-full ${isPolling ? 'bg-green-500' : 'bg-gray-300'}`}
                    />
                    {'videoDataRequested'}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span
                      className={`h-2 w-2 rounded-full ${isPolling ? 'animate-pulse bg-blue-500' : 'bg-gray-300'}`}
                    />
                    {isPolling ? 'Generating transcript with AI...' : 'Preparing analysis...'}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span className="h-2 w-2 rounded-full bg-gray-300" />
                    {'creatingSummary'}
                  </div>
                </div>
                {isPolling && (
                  <div className="mt-4 rounded-lg bg-blue-50 p-3">
                    <p className="text-sm text-blue-800">
                      💡 <strong>{'status'}:</strong> {'processingInProgress'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white">
        <div className="sticky top-0 z-10 border-b border-gray-200 bg-white">
          <div className="container py-4">
            <Button
              variant="ghost"
              onClick={handleBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="container py-8">
          <div className="mx-auto max-w-2xl">
            <Alert className="border-red-200 bg-red-50">
              <AlertDescription className="text-red-800">
                <div className="flex items-center justify-between">
                  <span>{error}</span>
                  <Button size="sm" onClick={handleRetry} className="ml-4" />
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  if (!videoData) return null;

  const videoId = extractVideoId(url);
  const isPreset = isPresetVideo(videoId);

  return (
    <WatchPageContainer video={videoData} isPreset={isPreset} locale={locale} pageType={pageType} />
  );
}
