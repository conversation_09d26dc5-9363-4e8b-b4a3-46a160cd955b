// Line View视图

'use client';

import { memo } from 'react';

import { cn } from '@/lib/utils';

import { useDragDetection, useWatchTrackActions, WatchTranscriptItem } from '../hooks';

interface LineByLineTranscriptProps {
  transcript: WatchTranscriptItem[];
  videoId: string;
}

export const LineByLineTranscript = memo(function LineByLineTranscript({
  transcript,
  videoId,
}: LineByLineTranscriptProps) {
  const { trackTimestampClick } = useWatchTrackActions();
  const { handleMouseDown, handleMouseMove, handleMouseUp } = useDragDetection();

  return (
    <ol className="flex flex-col gap-y-3">
      {transcript.map((item, index) => (
        <li
          key={index}
          data-timestamp={item.timestamp}
          className="-mx-2 flex cursor-pointer flex-row rounded-lg px-2 py-2 transition-colors hover:bg-muted hover:text-foreground"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={() =>
            handleMouseUp(() => {
              trackTimestampClick(item.timestamp, videoId);
              // TODO: 实现视频跳转功能
            })
          }
        >
          {/* 时间戳 */}
          <div className="w-[64px]">
            <div
              className={cn(
                'mr-4 h-[24px] rounded-lg bg-card-snips px-[4px] py-[3px] text-center text-sm !font-normal leading-[18px] shadow-sm',
              )}
            >
              {item.timestamp}
            </div>
          </div>

          {/* 内容 */}
          <div className="flex-1">
            <p className="whitespace-pre-wrap rounded-lg focus:underline focus:decoration-link focus:underline-offset-4">
              {item.speaker && (
                <span className="mr-2 font-medium text-blue-600">{item.speaker}:</span>
              )}
              {item.content}
            </p>
          </div>
        </li>
      ))}
    </ol>
  );
});
