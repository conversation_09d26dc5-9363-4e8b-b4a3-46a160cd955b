/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 呼吸动画 */
@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* 从左滑入 */
@keyframes slideIn {
  0% {
    width: 0;
  }
  100% {
    width: 32px;
  }
}

/* 从中间，纵向长出来 */
@keyframes bubbleGrow {
  0% {
    height: 0px;
    top: 9px;
  }
  100% {
    height: 12px;
    top: 6px;
  }
}

.animated-point {
  animation:
    fadeIn 0.5s ease-in-out,
    breathe 0.5s ease-in-out;
}

.animated-arc {
  animation:
    fadeIn 0.5s ease-in-out,
    slideIn 0.5s ease-in-out;
}

.point {
  position: absolute;
  z-index: 1;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--point-background, hsl(var(--card)));
  display: flex;
  justify-content: center;
  align-items: center;
}

.point button {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  border: 6px solid var(--point-background, hsl(var(--card)));
  border-radius: 50%;
  background: var(--color);
  transition: all 0.1s ease-in-out;
  cursor: pointer;
}

.point button:before {
  content: "";
  width: 20px;
  height: 20px;
  background-color: var(--color);
  opacity: 0.3;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: all 0.1s ease-in-out;
  z-index: 2;
}

.point button:hover::before {
  opacity: 0.3 !important;
  left: 50% !important;
  transform: translate(-50%, -50%) scale(1) !important;
}

.point button.point-collapse::before {
  opacity: 0.45;
  left: 66%;
  transform: translate(-50%, -50%) scale(0.4);
}

.arc {
  position: absolute;
  width: 28px;
  height: 30px;
  border-bottom-left-radius: 40%;
  border-left: 2px solid transparent;
  border-bottom: 2px solid transparent;
  border-color: var(--color);
}

.hiddenByH3 {
  display: none;
}

.hiddenByH4 {
  display: none;
}

.vline {
  background: var(--color);
  position: absolute;
  height: 100%;
  width: 2px;
  top: 20px;
  left: 5px;
}

.lastline {
  height: calc(100% - 40px);
}

.dark-blue {
  --color: var(--dark-blue-color);
}

.purple {
  --color: var(--purple-color);
}

.yellow {
  --color: var(--yellow-color);
}

.red {
  --color: var(--red-color);
}

.blue {
  --color: var(--blue-color);
}

.green {
  --color: var(--green-color);
}

.li {
  overflow-wrap: break-word;
  list-style-type: none;
  position: relative;
  margin-bottom: 8px;
  line-height: 1.6;
  color: hsl(var(--muted-foreground));
  transition: color 0.2s ease-in-out;
}

.li::before {
  content: "";
  position: absolute;
  left: -16px;
  top: 0.55em;
  width: 6px;
  height: 6px;
  background: hsla(var(--caption));
  border-radius: 50%;
}

.li:hover {
  color: hsl(var(--foreground));
}

.li.citation {
  cursor: pointer;
  padding-right: 24px;
}

.anchor {
  position: absolute;
  bottom: 4px;
  animation:
    fadeIn 0.1s ease-in-out,
    breathe 0.1s ease-in-out;
}

.citation-btn {
  display: none;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: hsl(var(--muted-foreground));
  transition: color 0.2s ease-in-out;
}

.citation-btn:hover {
  color: hsl(var(--foreground));
}

.li:hover .citation-btn {
  display: inline-flex;
}
