'use client';

import { useRouter } from '@i18n/navigation';
import { Button } from '@repo/ui/components/ui/button';
import { Input } from '@repo/ui/components/ui/input';
import { useEffect, useState } from 'react';
import { UpgradeModal } from '../../../../credits/components';
import { YouTubeCreditsManager } from '../../../../credits/manager';
import { YouTubePageConfig } from '../../config';

interface SearchBlockProps {
  config: YouTubePageConfig;
  onSubmit?: (url: string) => void;
  isLoading?: boolean;
  className?: string;
  enableCredits?: boolean; // 新增：是否启用credits功能
}

export default function SearchBlock({
  config,
  onSubmit,
  isLoading = false,
  className = '',
  enableCredits = true, // 默认不启用credits
}: SearchBlockProps) {
  // const t = useTranslations('Credits');
  const [url, setUrl] = useState('');
  const [creditsManager, setCreditsManager] = useState<YouTubeCreditsManager | null>(null);
  const [isCreditsManagerReady, setIsCreditsManagerReady] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeMessage, setUpgradeMessage] = useState('');
  const router = useRouter();

  const [showError, setShowError] = useState(false);

  // 在客户端初始化 creditsManager
  useEffect(() => {
    if (enableCredits && typeof window !== 'undefined') {
      try {
        const manager = new YouTubeCreditsManager();
        setCreditsManager(manager);
        setIsCreditsManagerReady(true);
      } catch (error) {
        console.warn('Failed to initialize credits manager:', error);
        setIsCreditsManagerReady(true); // 即使失败也设为ready，避免无限等待
      }
    } else {
      setIsCreditsManagerReady(true); // 如果不启用credits，直接设为ready
    }
  }, [enableCredits]);

  const handleSubmit = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const trimmedUrl = url.trim();

    if (!trimmedUrl || !isValidYouTubeUrl(trimmedUrl)) {
      setShowError(true);
      return;
    }

    // 清除错误状态
    setShowError(false);

    // 如果启用了credits且有pageType，进行credits检查
    if (enableCredits && creditsManager && config.pageType && isCreditsManagerReady) {
      const pageType = config.pageType as 'transcript' | 'summary';

      // 检查是否可以访问视频详情页
      const accessCheck = creditsManager.canAccessVideoDetail(trimmedUrl, pageType);

      if (!accessCheck.canAccess) {
        // 显示升级提示
        setUpgradeMessage(accessCheck.upgradePrompt || 'Usage limit reached');
        setShowUpgradeModal(true);
        return;
      }

      // 记录视频访问
      const recorded = creditsManager.recordVideoAccess(trimmedUrl, pageType);

      if (!recorded) {
        setUpgradeMessage(
          'Failed to record video access. Please try again or upgrade your account',
        );
        setShowUpgradeModal(true);
        return;
      }
    }

    if (onSubmit) {
      // 如果有自定义的onSubmit处理器，使用它
      onSubmit(trimmedUrl);
    } else {
      // 根据pageType跳转到不同的详情页面
      if (config.pageType === 'transcript') {
        router.push(`/youtube-transcript-generator-detail?url=${encodeURIComponent(trimmedUrl)}`);
      } else if (config.pageType === 'summary') {
        router.push(`/youtube-summary-detail?url=${encodeURIComponent(trimmedUrl)}`);
      } else {
        // 默认跳转到 transcript 页面（如果没有明确指定pageType）
        router.push(`/youtube-transcript-generator-detail?url=${encodeURIComponent(trimmedUrl)}`);
      }
    }
  };

  const handleUpgrade = () => {
    console.log('Redirecting to upgrade page');
    setShowUpgradeModal(false);
  };

  const isValidYouTubeUrl = (url: string) => {
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
    return youtubeRegex.test(url);
  };

  return (
    <div className={`mx-auto mb-[100px] max-w-4xl ${className}`}>
      {/* Credits状态显示 */}
      {/* 不显示Credits状态栏 */}
      {/* {enableCredits && config.pageType && (
        <StatusBar
          pageType={config.pageType as "transcript" | "summary"}
          className="mb-4"
        />
      )} */}

      <div className="relative flex items-center rounded-full border border-gray-200 bg-white px-1 py-1 shadow-sm">
        <div className="mr-4 flex items-center text-gray-400">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13.5 2L3 13.5L11.5 22L22 10.5L13.5 2Z"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        <Input
          type="url"
          placeholder={config.searchPlaceholder}
          value={url}
          onChange={(e) => {
            setUrl(e.target.value);
            // 当用户开始输入时清除错误状态
            if (showError) {
              setShowError(false);
            }
          }}
          className="flex-1 border-0 bg-transparent text-base placeholder:text-gray-400 focus:outline-none focus:ring-0"
          disabled={isLoading}
        />

        <Button
          onClick={handleSubmit}
          disabled={!url.trim() || isLoading || (enableCredits && !isCreditsManagerReady)}
          className="ml-4 rounded-full bg-black px-10 py-7 text-white hover:bg-gray-800 active:bg-gray-900 disabled:cursor-not-allowed disabled:bg-black disabled:opacity-100"
        >
          <span className="mr-2">✨</span>
          {isLoading
            ? 'Generating...'
            : enableCredits && !isCreditsManagerReady
              ? 'Loading...'
              : config.buttonText}
        </Button>
      </div>

      {showError && (
        <p className="mt-3 text-center text-sm text-red-500">Please enter a valid YouTube URL</p>
      )}

      {/* 升级模态框 */}
      {enableCredits && (
        <UpgradeModal
          isOpen={showUpgradeModal}
          onClose={() => setShowUpgradeModal(false)}
          message={upgradeMessage}
          onUpgrade={handleUpgrade}
        />
      )}
    </div>
  );
}
