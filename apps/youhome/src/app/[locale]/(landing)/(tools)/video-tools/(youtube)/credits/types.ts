// YouTube SEO页面 Credits系统类型定义

export type PageType = 'transcript' | 'summary';

export interface VideoLevelCredits {
  videoId: string; // 视频唯一标识
  url: string; // 原始URL
  firstAccessTime: string; // 首次访问时间
  lastAccessTime: string; // 最后访问时间
}

export interface PageLevelCredits {
  pageType: 'transcript' | 'summary'; // 页面类型
  videosAccessed: string[]; // 已访问的视频ID列表
  videosLimit: number; // 视频查看限制 (默认3个)
  lastResetDate: string; // 最后重置日期
  firstVisitTime: string; // 首次访问时间
}

export interface UserCreditsState {
  pageCredits: {
    transcript: PageLevelCredits;
    summary: PageLevelCredits;
  };
  videoCredits: Record<string, VideoLevelCredits>; // videoId -> credits
  dailyUsage: {
    date: string;
    totalVideosViewed: number;
  };
}

export interface CreditsCheckResult {
  canAccess: boolean;
  reason?: 'page_limit_exceeded' | 'daily_limit_exceeded';
  remainingVideos?: number;
  upgradePrompt?: string;
}
