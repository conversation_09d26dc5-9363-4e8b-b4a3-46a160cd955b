// Youtube模板頁面結構

import { fetchCollectionItems } from '@/cms/cms-util';
import { CollectionType } from '@/cms/types';
import Examples from './components/Examples';
import Extension from './components/Extension';
import FeatDescBoxes from './components/FeatDescBoxes';
import Questions from './components/Questions';
import SearchBlock from './components/SearchBlock';
import Testimonials from './components/Testimonials';
import Title from './components/Title';
import UseCasesBoxes from './components/UseCasesBoxes';
import UserGuide from './components/UserGuide';
import { YouTubePageConfig } from './config';

interface YouTubePageTemplateProps {
  config: YouTubePageConfig;
  enableCredits?: boolean; // 新增：是否启用credits功能
}

export default async function YouTubePageTemplate({
  config,
  enableCredits = true, // 默认启用credits
}: YouTubePageTemplateProps) {
  const testimonials = await fetchCollectionItems(CollectionType.Testimonials);

  return (
    <div>
      <div className="container pt-[80px] md:pt-[128px]">
        <Title config={config} />
        <SearchBlock config={config} enableCredits={enableCredits} />
        <Examples config={config} />
        <Extension />
        <UserGuide config={config} />
        <FeatDescBoxes config={config} />
      </div>
      <UseCasesBoxes />
      <Testimonials testimonials={testimonials} />
      <Questions />
    </div>
  );
}
