import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { setRequestLocale } from 'next-intl/server';
import { extractVideoIdFromInput, isValidVideoId } from '../containers/detail/utils';
import WatchPageTemplate from '../containers/detail/WatchPageTemplate';

export async function generateMetadata({
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ url?: string }>;
}): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;

  if (!resolvedSearchParams.url) {
    return {
      title: 'YouTube Summary - YouMind',
      description: 'Generate AI-powered summary for YouTube videos. Free online tool.',
      robots: 'noindex',
    };
  }

  const extractedId = extractVideoIdFromInput(resolvedSearchParams.url);
  if (!extractedId || !isValidVideoId(extractedId)) {
    return {
      title: 'Invalid Video - YouMind',
      robots: 'noindex',
    };
  }

  return {
    title: `YouTube Video Summary - YouMind`,
    description: `Generate AI-powered summary for YouTube video. Free online tool.`,
    robots: 'noindex', // 实时生成的页面不索引
    alternates: {
      canonical: `https://youmind.ai/youtube-summary-detail?url=${encodeURIComponent(resolvedSearchParams.url)}`,
    },
  };
}

export default async function YouTubeTranscriptGeneratorDetailPage({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ url?: string }>;
}) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;

  setRequestLocale(resolvedParams.locale);

  // 检查是否有URL参数
  if (!resolvedSearchParams.url) {
    notFound();
  }

  const extractedId = extractVideoIdFromInput(resolvedSearchParams.url);

  // 如果URL无效，返回404
  if (!extractedId || !isValidVideoId(extractedId)) {
    notFound();
  }

  return (
    <WatchPageTemplate
      url={resolvedSearchParams.url}
      locale={resolvedParams.locale}
      pageType="summary"
    />
  );
}
