'use client';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  onUpgrade?: () => void;
}

export function UpgradeModal({ isOpen, onClose, message, onUpgrade }: UpgradeModalProps) {
  // const t = useTranslations('Credits');

  if (!isOpen) return null;

  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else {
      console.log('Navigate to upgrade page');
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
        <div className="mb-4 text-center">
          <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
            <svg
              className="h-6 w-6 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900">unlockUnlimited</h3>
          <p className="text-sm text-gray-600">{message}</p>
        </div>

        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 rounded-lg border border-gray-300 px-4 py-2 font-medium text-gray-700 transition-colors hover:bg-gray-50"
          >
            laterButton
          </button>
          <button
            onClick={handleUpgrade}
            className="flex-1 rounded-lg bg-blue-600 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-700"
          >
            upgradeButton
          </button>
        </div>

        <div className="mt-3 text-center">
          <p className="text-xs text-gray-500">Upgrade note</p>
        </div>
      </div>
    </div>
  );
}
