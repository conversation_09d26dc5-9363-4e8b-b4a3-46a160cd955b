'use client';

import { Marquee } from '@repo/ui/components/magicui/marquee';
import Image from 'next/image';
import { CollectionResponse, Testimonial } from '@/cms/types';
import { Quote2 } from '@/components/icon/quote2';

export default function Testimonials(props: { testimonials: CollectionResponse<Testimonial> }) {
  const { testimonials } = props;

  if (!testimonials || testimonials.docs.length === 0) {
    return null;
  }

  return (
    <section className="mx-auto mb-[100px] mt-[120px] w-full md:mt-[200px]">
      <div className="mx-auto">
        <h2 className="mx-auto mb-8 max-w-7xl px-8 text-center font-sans-title text-3xl md:mb-16 md:text-4xl lg:text-[42px]">
          What our users say
        </h2>

        <div className="relative overflow-hidden">
          {/* 左侧渐变遮罩 */}
          {/* <div className="pointer-events-none absolute left-0 top-0 z-10 h-full w-12 bg-gradient-to-r from-background to-transparent" /> */}
          {/* 右侧渐变遮罩 */}
          {/* <div className="pointer-events-none absolute right-0 top-0 z-10 h-full w-12 bg-gradient-to-l from-background to-transparent" /> */}

          <Marquee pauseOnHover className="gap-4 py-4 [--duration:60s] md:gap-7 lg:gap-9">
            {testimonials.docs.map((item) => (
              <div key={item.id}>
                <div className="relative w-[306px] flex-shrink-0 overflow-hidden rounded-3xl border border-card-snips bg-[#F7F7F8] p-6 md:w-[340px] lg:w-[376px]">
                  <Quote2 size={40} />

                  <div className="mt-6 inline-flex w-full max-w-[320px] flex-col items-start justify-start gap-6">
                    <div className="flex flex-col items-start justify-start gap-4 self-stretch">
                      <div className="line-clamp-2 justify-start self-stretch text-xl leading-7 text-foreground">
                        {item.title || 'YouMind can reduce the number of tools I need to use.'}
                      </div>
                      <div className="line-clamp-6 justify-start self-stretch font-serif text-base font-normal leading-relaxed text-muted-foreground">
                        {item.comment}
                      </div>
                    </div>
                  </div>

                  <div className="mt-7 inline-flex items-center justify-start gap-3">
                    {item.thumbnail &&
                    typeof item.thumbnail !== 'number' &&
                    (item.thumbnail.url || item.thumbnail.sizes?.thumbnail?.url) ? (
                      <Image
                        src={item.thumbnail.url || (item.thumbnail.sizes?.thumbnail?.url as string)}
                        alt={item.name}
                        width={44}
                        height={44}
                        className="h-11 w-11 rounded-full object-cover"
                      />
                    ) : (
                      <div className="flex h-11 w-11 items-center justify-center rounded-full bg-accent">
                        <span className="text-lg font-medium text-foreground">
                          {(item.name || '').charAt(0)}
                        </span>
                      </div>
                    )}
                    <div className="inline-flex flex-col items-start justify-start">
                      <div className="justify-start text-base leading-normal text-foreground/90">
                        {item.name}
                      </div>
                      {item.role && (
                        <div className="justify-start text-sm font-normal leading-tight text-caption">
                          {item.role}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </Marquee>
        </div>
      </div>
    </section>
  );
}
