import { YouTubePageConfig } from '../../config';

interface TitleProps {
  config: YouTubePageConfig;
  className?: string;
}

export default function Title({ config, className = '' }: TitleProps) {
  return (
    <section
      className={`relative mx-auto mb-[40px] w-full pb-10 pt-12 md:pb-0 md:pt-16 lg:pt-20 ${className}`}
    >
      <div className="mx-auto flex max-w-7xl flex-col items-center px-6 text-center lg:px-8">
        <h1 className="mb-4 bg-gradient-to-l from-[rgba(242,135,54,0.88)] via-[rgba(193,86,240,0.88)] via-[rgba(236,100,216,0.88)] via-[rgba(245,92,137,0.88)] to-[rgba(101,81,246,0.88)] bg-clip-text font-sans-title text-4xl text-transparent md:mb-6 md:text-5xl lg:text-[56px]">
          {config.title}
        </h1>
        <p className="mx-auto mb-8 max-w-3xl font-sans-title text-base text-secondary-foreground md:mb-10 md:max-w-4xl md:text-lg lg:text-xl">
          {config.subtitle1}
          <br />
          {config.subtitle2}
        </p>
      </div>
    </section>
  );
}
