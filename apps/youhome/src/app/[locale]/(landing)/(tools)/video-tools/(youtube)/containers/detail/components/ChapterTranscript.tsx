// Chapter View视图

'use client';

import { Avatar, AvatarFallback } from '@repo/ui/components/ui/avatar';

import { useDragDetection, useWatchTrackActions, WatchTranscriptItem } from '../hooks';

interface ChapterTranscriptProps {
  transcript: WatchTranscriptItem[];
  videoId: string;
}

interface GroupedTranscript {
  speaker: string;
  avatar?: string;
  content: Array<{
    timestamp: string;
    text: string;
  }>;
}

export function ChapterTranscript({ transcript, videoId }: ChapterTranscriptProps) {
  const { trackTimestampClick } = useWatchTrackActions();
  const { handleMouseDown, handleMouseMove, handleMouseUp } = useDragDetection();

  // 按说话人分组转录内容
  const groupedTranscript = transcript.reduce<GroupedTranscript[]>((acc, item) => {
    const speaker = item.speaker || 'Speaker';
    const lastGroup = acc[acc.length - 1];

    if (lastGroup && lastGroup.speaker === speaker) {
      lastGroup.content.push({
        timestamp: item.timestamp,
        text: item.content,
      });
    } else {
      acc.push({
        speaker,
        avatar: generateAvatarColor(speaker),
        content: [
          {
            timestamp: item.timestamp,
            text: item.content,
          },
        ],
      });
    }

    return acc;
  }, []);

  // 生成头像颜色
  function generateAvatarColor(speaker: string): string {
    const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-pink-500'];
    const colorIndex = speaker.length % colors.length;
    return colors[colorIndex] || 'bg-gray-500';
  }

  return (
    <div className="space-y-6 text-base leading-6 text-foreground">
      {groupedTranscript.map((group, groupIndex) => (
        <div key={groupIndex}>
          <div className="mb-3 flex items-center gap-x-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback className={group.avatar}>
                {group.speaker.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex items-center">
              <span className="font-medium text-gray-900">{group.speaker}</span>
            </div>
          </div>

          <div className="flex flex-col gap-y-3 pl-11">
            {group.content.map((item, itemIndex) => (
              <div key={itemIndex} className="flex flex-col">
                <button
                  className="mb-1 self-start font-mono text-xs text-gray-500 transition-colors hover:text-blue-600"
                  onClick={() => handleMouseUp(() => trackTimestampClick(item.timestamp, videoId))}
                  data-timestamp={item.timestamp}
                >
                  {item.timestamp}
                </button>

                <div
                  className="-mx-2 -my-1 cursor-pointer rounded-md px-2 py-1 transition-colors hover:bg-gray-50 hover:text-gray-900"
                  data-timestamp={item.timestamp}
                  data-speaker={group.speaker}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={() =>
                    handleMouseUp(() => trackTimestampClick(item.timestamp, videoId))
                  }
                >
                  <p className="leading-relaxed">{item.text}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {groupedTranscript.length === 0 && (
        <div className="flex flex-col items-center py-8 text-center">
          <div className="text-gray-500">No transcript content available</div>
        </div>
      )}
    </div>
  );
}
