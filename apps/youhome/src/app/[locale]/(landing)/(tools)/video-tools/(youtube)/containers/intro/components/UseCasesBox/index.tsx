import Image from 'next/image';

interface UseCaseBoxProps {
  image: string;
  title: string;
  description: string;
}

export default function UseCasesBox({ image, title, description }: UseCaseBoxProps) {
  return (
    <div
      className="rounded-3xl border p-6 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
      style={{ backgroundColor: '#F7F7F8', borderColor: '#EDEDEF' }}
    >
      <div className="mb-4 flex justify-center">
        <Image src={image} alt={title} width={264} height={264} />
      </div>

      <h3 className="mb-3 text-lg font-semibold">{title}</h3>
      <p className="text-sm leading-relaxed text-secondary-foreground">{description}</p>
    </div>
  );
}
