# YouTube SEO页面 Credits系统

## 🎯 概述

这是一个专为YouTube SEO页面设计的Credits限制系统，所有代码都集中在 `(landing)` 文件夹内，确保代码内聚性。

### **限制策略**
- **页面级**: 每个页面（transcript/summary）最多查看3个视频
- **视频级**: 每个视频详情页最多进行3次AI对话

### **技术特点**
- ✅ 设备指纹 + localStorage双重验证
- ✅ 服务端渲染友好
- ✅ TypeScript类型安全
- ✅ 模块化组件设计
- ✅ 统一组件架构（无需维护重复的WithCredits版本）

## 📂 文件结构

```
src/app/[locale]/(landing)/
├── credits/                      # Credits系统核心
│   ├── types.ts                 # 类型定义
│   ├── manager.ts               # YouTubeCreditsManager
│   ├── components/              # UI组件
│   │   ├── index.ts            # 导出索引
│   │   ├── StatusBar.tsx       # 状态显示
│   │   ├── UpgradeModal.tsx    # 升级弹窗
│   │   └── AiChat.tsx          # AI聊天组件
│   └── README.md               # 使用指南
├── containers/youtube/
│   ├── YouTubePageTemplate.tsx  # 统一页面模板（支持可选credits）
│   ├── SearchBlock/
│   │   └── index.tsx           # 统一搜索组件（支持可选credits）
│   └── Examples/
│       └── index.tsx           # 统一示例组件（智能检测credits）
└── ...
```

## 🚀 快速集成

### **1. 主页面集成**

使用统一的模板和可选的credits参数：

```tsx
// 启用Credits功能的页面
import YouTubePageTemplate from "../containers/youtube/YouTubePageTemplate";

export default function YouTubeTranscriptGeneratorPage() {
  return <YouTubePageTemplate config={transcriptConfig} enableCredits={true} />;
}

// 不启用Credits的页面（默认）
export default function YouTubeBasicPage() {
  return <YouTubePageTemplate config={basicConfig} />;
}
```

### **2. 视频详情页集成**

```tsx
import { AiChat } from '../credits/components';

export default function VideoDetailPage({ searchParams }: { searchParams: { url: string } }) {
  return (
    <div>
      {/* 视频内容 */}
      <VideoPlayer url={searchParams.url} />
      
      {/* AI对话组件 */}
      <AiChat 
        videoUrl={searchParams.url}
        onChatSubmit={async (message) => {
          const response = await callYourAiApi(message, searchParams.url);
          return response;
        }}
      />
    </div>
  );
}
```

## 💾 核心API

### **YouTubeCreditsManager**

```typescript
import { YouTubeCreditsManager } from './credits/manager';

const manager = new YouTubeCreditsManager();

// 检查视频访问权限
const accessCheck = manager.canAccessVideoDetail(url, 'transcript');
if (accessCheck.canAccess) {
  // 记录访问并跳转
  manager.recordVideoAccess(url, 'transcript');
}

// 检查AI对话权限
const chatCheck = manager.canUseAiChat(url);
if (chatCheck.canAccess) {
  // 消耗对话次数
  manager.consumeAiChat(url);
}

// 获取使用统计
const stats = manager.getUsageStats();
```

### **UI组件**

```tsx
import { StatusBar, UpgradeModal, AiChat } from './credits/components';

// 状态显示
<StatusBar 
  pageType="transcript"
  currentVideoUrl={videoUrl}
  showVideoLevelStats={true}
/>

// 升级弹窗
<UpgradeModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  message="您已达到使用限制"
  onUpgrade={() => console.log('跳转升级')}
/>

// AI聊天
<AiChat
  videoUrl={videoUrl}
  onChatSubmit={async (msg) => await callAI(msg)}
/>
```

## 🎛️ 组件架构

### **统一设计模式**

所有组件都采用了智能的可选credits支持：

```typescript
// SearchBlock - 通过enableCredits参数控制
<SearchBlock config={config} enableCredits={true} />

// Examples - 智能检测config.pageType自动启用
<Examples config={config} /> // 自动根据pageType启用credits

// YouTubePageTemplate - 通过enableCredits控制整体功能
<YouTubePageTemplate config={config} enableCredits={true} />
```

### **向后兼容性**

- ✅ 现有的非credits页面无需修改
- ✅ 通过参数控制是否启用credits功能
- ✅ 智能检测，无需重复代码

## 🛠️ 开发调试

### **重置Credits**
```typescript
const manager = new YouTubeCreditsManager();
manager.resetCredits(); // 清空所有数据
```

### **查看状态**
```typescript
console.log(manager.getCreditsState());
console.log(manager.getUsageStats());
```

## 🏗️ 架构优势

1. **代码统一** - 只需维护一套组件，无重复代码
2. **灵活控制** - 通过参数轻松控制是否启用credits
3. **向后兼容** - 现有代码无需修改即可继续工作
4. **智能检测** - Examples组件根据配置自动启用credits
5. **易于维护** - 统一的组件架构，降低维护成本