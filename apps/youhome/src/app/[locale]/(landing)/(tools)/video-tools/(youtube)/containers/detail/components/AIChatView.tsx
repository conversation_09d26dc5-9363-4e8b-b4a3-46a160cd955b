'use client';

import { Button } from '@repo/ui/components/ui/button';
import { <PERSON><PERSON>, Eye, Key, Pin, User } from 'lucide-react';
import { useState } from 'react';

import { WatchVideoData } from '../types';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface AIChatViewProps {
  video: WatchVideoData;
}

// 简单的客户端限制管理
class GuestLimitsManager {
  private static readonly MAX_CONVERSATIONS = 3;
  private static readonly STORAGE_KEY = 'guest_conversation_count';

  static getConversationCount(): number {
    if (typeof window === 'undefined') return 0;
    return parseInt(localStorage.getItem(GuestLimitsManager.STORAGE_KEY) || '0');
  }

  static incrementConversationCount(): void {
    if (typeof window === 'undefined') return;
    const count = GuestLimitsManager.getConversationCount() + 1;
    localStorage.setItem(GuestLimitsManager.STORAGE_KEY, count.toString());
  }

  static canStartConversation(): boolean {
    return GuestLimitsManager.getConversationCount() < GuestLimitsManager.MAX_CONVERSATIONS;
  }

  static getRemainingConversations(): number {
    return Math.max(
      0,
      GuestLimitsManager.MAX_CONVERSATIONS - GuestLimitsManager.getConversationCount(),
    );
  }
}

export function AIChatView({ video }: AIChatViewProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const _handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // 检查免登录限制
    if (!GuestLimitsManager.canStartConversation()) {
      // TODO: 显示登录提示
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // 模拟AI响应
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: `Based on the video "${video.title}", I can help you understand the key points about visiting Norway's Lofoten Islands. The video covers essential travel tips including the best time to visit, transportation options, must-see attractions, and photography advice. Would you like me to elaborate on any specific aspect?`,
          timestamp: new Date(),
        };

        setMessages((prev) => [...prev, aiResponse]);
        setIsLoading(false);

        // 更新限制计数
        GuestLimitsManager.incrementConversationCount();
      }, 1500);
    } catch (error) {
      console.error('Error sending message:', error);
      setIsLoading(false);
    }
  };

  const handleLoginRedirect = () => {
    window.location.href = '/sign-in';
  };

  // 渲染欢迎界面
  const renderWelcomeView = () => (
    <div className="flex w-3/4 flex-col items-start justify-start py-12 text-left">
      <div className="mb-8 justify-start">
        <h1 className="mb-2 text-2xl font-medium text-gray-900">
          {/* TODO: i18n */}👋 Hi, I&apos;m your AI assistant.
        </h1>
        <p className="w-3/4 text-sm font-light text-gray-600">
          {/* TODO: i18n */}
          Your personal AI assistant is here—ask me anything about this file and unlock the magic of
          YouMind AI right now!
        </p>
      </div>

      {/* 建议按钮 */}
      <div className="mb-8 w-full max-w-md space-y-3">
        <Button
          variant="ghost"
          className="w-full justify-between rounded-3xl border-none bg-gray-50 hover:bg-gray-100"
          onClick={handleLoginRedirect}
        >
          <div className="flex items-center gap-3">
            <Pin className="h-4 w-4 text-red-500" />
            <span>
              {/* TODO: i18n */}
              Extract 5 key points.
            </span>
          </div>
          <span>→</span>
        </Button>

        <Button
          variant="ghost"
          className="w-full justify-between rounded-3xl border-none bg-gray-50 hover:bg-gray-100"
          onClick={handleLoginRedirect}
        >
          <div className="flex items-center gap-3">
            <Key className="h-4 w-4 text-yellow-500" />
            <span>
              {/* TODO: i18n */}
              What insights does this content offer?
            </span>
          </div>
          <span>→</span>
        </Button>

        <Button
          variant="ghost"
          className="w-full justify-between rounded-3xl border-none bg-gray-50 hover:bg-gray-100"
          onClick={handleLoginRedirect}
        >
          <div className="flex items-center gap-3">
            <Eye className="text-brown-500 h-4 w-4" />
            <span>
              {/* TODO: i18n */}
              List 5 keywords and explain their meanings.
            </span>
          </div>
          <span>→</span>
        </Button>
      </div>

      {/* 主要 AI 聊天按钮 */}
      <div className="mt-8">
        <Button
          size="lg"
          className="rounded-3xl bg-gray-900 text-white hover:bg-gray-800"
          onClick={handleLoginRedirect}
        >
          <Bot className="mr-2 h-5 w-5" />
          {/* TODO: i18n */}
          AI Chat in YouMind
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* 聊天消息 */}
      <div className="max-h-[600px] min-h-[400px] space-y-4 overflow-y-auto rounded-lg p-4">
        {messages.length === 0
          ? renderWelcomeView()
          : messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                }`}
              >
                <div
                  className={`flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full ${
                    message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'
                  }`}
                >
                  {message.role === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>
                <div
                  className={`max-w-[80%] flex-1 ${
                    message.role === 'user' ? 'text-right' : 'text-left'
                  }`}
                >
                  <div
                    className={`inline-block rounded-lg p-3 ${
                      message.role === 'user'
                        ? 'rounded-br-sm bg-blue-500 text-white'
                        : 'rounded-bl-sm bg-white text-gray-900 shadow-sm'
                    }`}
                  >
                    <p className="whitespace-pre-wrap">{message.content}</p>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}

        {isLoading && (
          <div className="flex gap-3">
            <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-300">
              <Bot className="h-4 w-4 text-gray-600" />
            </div>
            <div className="flex-1">
              <div className="inline-block rounded-lg rounded-bl-sm bg-white p-3 shadow-sm">
                <div className="flex space-x-1">
                  <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400" />
                  <div
                    className="h-2 w-2 animate-bounce rounded-full bg-gray-400"
                    style={{ animationDelay: '0.1s' }}
                  />
                  <div
                    className="h-2 w-2 animate-bounce rounded-full bg-gray-400"
                    style={{ animationDelay: '0.2s' }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
