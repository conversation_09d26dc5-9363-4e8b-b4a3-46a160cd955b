import clsx from 'clsx';
import Image from 'next/image';

interface Props {
  image: string;
  title: string;
  description: string;
  isMobile: boolean;
}

const FeatDescBox = ({ image, title, description }: Props) => {
  return (
    <div className={clsx('flex flex-col transition-all duration-300')}>
      <div className="flex h-[160px] items-center justify-start rounded-[16px] bg-white pl-6">
        <Image src={image} alt={title} width={120} height={120} />
      </div>

      <h3 className="mb-2 mt-3 text-[22px] font-medium leading-[24px] text-foreground md:text-[24px] md:leading-[38px]">
        {title}
      </h3>
      <p className="text-base text-muted-foreground md:text-[16px] md:leading-[26px]">
        {description}
      </p>
    </div>
  );
};

export default FeatDescBox;
