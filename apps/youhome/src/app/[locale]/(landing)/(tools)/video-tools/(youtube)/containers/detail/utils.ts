/**
 * YouTube Watch 页面工具函数
 *
 * 这个文件包含处理YouTube视频数据的工具函数，支持统一路由系统。
 *
 * === 后端API接口说明 ===
 *
 * 【当前使用的接口】
 *
 * 1. 获取缓存视频数据接口
 *    POST /api/v1/snip/getSnipByUrl
 *    功能: 根据YouTube URL获取已缓存的视频数据
 *    请求: { url: string }
 *    返回: WatchVideoData | null (如果有缓存返回数据，否则返回null)
 *
 * 2. 创建视频资源接口
 *    POST /api/v1/tryCreateSnipByUrl
 *    功能: 通过YouTube URL创建新的视频资源并进行转录分析
 *    请求: { url: string }
 *    返回: WatchVideoData (新创建的视频数据)
 *
 *
 * === 数据流设计 ===
 *
 * 1. 用户通过URL访问视频页面 (不再使用videoId)
 * 2. 检查是否为预设视频 → 返回静态数据 (通过extractVideoId获取videoId判断)
 * 3. 调用 /api/v1/snip/getSnipByUrl 检查数据库缓存
 * 4. 如果返回null，调用 /api/v1/tryCreateSnipByUrl 创建新资源
 * 5. 根据后端情况决定是否需要使用适配器将返回数据统一转换为 WatchVideoData 格式，还是直接拿到WatchVideoData 格式
 * 6. 如果API失败，降级使用mock数据保证用户体验
 *
 * === 实施优先级 ===
 *
 * P1: ✅ 复用现有 getSnipByUrl 和 tryCreateSnipByUrl 接口
 * P2: 📋 完善错误处理和降级策略
 * P3: 🔄 添加缓存机制和性能优化
 * P4: 🌍 多语言转录支持
 */

import { WatchVideoData } from './types';

// 轮询检查视频数据状态
async function pollVideoStatus(
  url: string,
  maxAttempts: number = 30,
  interval: number = 2000,
): Promise<WatchVideoData | null> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const response = await fetch('/api/v1/snip/getSnipByUrl', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data && !data.status) {
          // status字段消失，表示处理完成
          return data;
        }

        if (data && data.status === 'fetching') {
          // 仍在处理中，继续等待
          console.log(`Video processing in progress... attempt ${attempt + 1}/${maxAttempts}`);
          await new Promise((resolve) => setTimeout(resolve, interval));
          continue;
        }
      }
    } catch (error) {
      console.error(`Polling attempt ${attempt + 1} failed:`, error);
    }

    // 每次失败后稍微增加等待时间
    await new Promise((resolve) => setTimeout(resolve, interval));
  }

  console.error('Polling timeout: Video processing took too long');
  return null;
}

// 从不同数据源获取视频数据的函数
export async function fetchVideoData(url: string): Promise<WatchVideoData | null> {
  // 数据获取流程：
  // 1. 首先检查是否有预设的静态数据
  // 2. 调用 getSnipByUrl 检查数据库缓存
  // 3. 如果缓存为空，调用 tryCreateSnipByUrl 创建新资源
  // 4. 如果返回status="fetching"，则轮询等待处理完成
  // 注意：如果后端直接返回 WatchVideoData 格式，则不需要适配器转换

  try {
    // 第一步：检查预设视频
    const videoId = extractVideoId(url);
    if (videoId && isValidVideoId(videoId)) {
      // 这里可以添加预设视频检查逻辑
      // 但通常在组件层面已经处理了
    }

    // 第二步：尝试从缓存获取数据
    const cacheResponse = await fetch('/api/v1/snip/getSnipByUrl', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url }),
    });

    if (cacheResponse.ok) {
      const cachedData = await cacheResponse.json();
      if (cachedData) {
        // 检查是否还在处理中
        if (cachedData.status === 'fetching') {
          console.log('Video is being processed, starting polling...');
          return await pollVideoStatus(url);
        }

        // 🤔 如果后端返回 WatchVideoData 格式，直接返回
        return cachedData;
        // 🔄 如果后端返回 SnipVO 格式，需要转换
        // return adaptSnipToWatchVideoData(cachedData);
      }
    }

    // 第三步：缓存中没有数据，创建新资源
    const createResponse = await fetch('/api/v1/tryCreateSnipByUrl', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url }),
    });

    if (createResponse.ok) {
      const newData = await createResponse.json();
      if (newData) {
        // 检查是否还在处理中
        if (newData.status === 'fetching') {
          console.log('Video created and being processed, starting polling...');
          return await pollVideoStatus(url);
        }

        // 🤔 如果后端返回 WatchVideoData 格式，直接返回
        return newData;
        // 🔄 如果后端返回 SnipVO 格式，需要转换
        // return adaptSnipToWatchVideoData(newData);
      }
    }

    throw new Error('Failed to fetch or create video data');
  } catch (error) {
    console.error('Error fetching video data:', error);
    return null;
  }
}

// 从 URL 提取视频 ID
export function extractVideoId(url: string): string {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
  const match = url.match(regex);
  return match ? match[1] || '' : '';
}

// 从任何输入（URL或videoId）获取有效的videoId
export function extractVideoIdFromInput(input: string): string {
  // 如果输入已经是一个看起来像videoId的字符串（11个字符），直接返回
  if (input && input.length === 11 && /^[a-zA-Z0-9_-]+$/.test(input)) {
    return input;
  }

  // 否则尝试从URL中提取
  return extractVideoId(input);
}

// 验证是否为有效的YouTube videoId格式
export function isValidVideoId(videoId: string): boolean {
  return Boolean(videoId && videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId));
}

// 从videoId构建标准的YouTube URL
export function buildYouTubeUrl(videoId: string): string {
  return `https://www.youtube.com/watch?v=${videoId}`;
}

// 判断输入是否为YouTube URL
export function isYouTubeUrl(input: string): boolean {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)/;
  return regex.test(input);
}

// 获取transcript文本内容
function getTranscriptText(transcript: WatchVideoData['transcript']): string {
  // 查找当前激活的内容
  const currentContent = transcript.contents.find(
    (content) => content.id === transcript.current_content_id,
  );

  return currentContent?.plain || '';
}

// 生成 JSON-LD 结构化数据用于 SEO
export function generateVideoJsonLd(video: WatchVideoData) {
  // 获取概述内容
  const currentOverview = video.overview.contents.find(
    (content) => content.id === video.overview.current_content_id,
  );
  const description = `${currentOverview?.plain?.slice(0, 200)}...` || '';

  // 获取第一个作者信息
  const primaryAuthor = video.author[0] || { name: 'Unknown', picture: '' };

  return {
    '@context': 'https://schema.org',
    '@type': 'VideoObject',
    name: video.title,
    description,
    uploadDate: new Date(video.published_at || video.created_at).toISOString(),
    thumbnailUrl: video.hero_image_url,
    contentUrl: video.play_url,
    embedUrl: `https://www.youtube.com/embed/${video.id}`,
    author: {
      '@type': 'Person',
      name: primaryAuthor.name,
    },
    publisher: {
      '@type': 'Organization',
      name: 'YouMind',
      url: 'https://youmind.ai',
    },
    interactionStatistic: {
      '@type': 'InteractionCounter',
      interactionType: { '@type': 'WatchAction' },
      userInteractionCount: parseInt(video.views.replace(/[^\d]/g, '')) || 0,
    },
    transcript: {
      '@type': 'MediaReviewItem',
      text: getTranscriptText(video.transcript),
    },
  };
}

// 格式化视频时长
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 生成视频的完整 meta 标签
export function generateVideoMeta(video: WatchVideoData) {
  return {
    title: `${video.title} - YouMind YouTube Transcript`,
    description: `Watch and get AI-powered transcript and overview of "${video.title}". Free online YouTube video analysis tool.`,
    openGraph: {
      title: `${video.title} - YouMind`,
      description: `AI-powered transcript and overview`,
      images: [video.hero_image_url],
      type: 'video.other',
      video: {
        url: video.play_url,
        type: 'text/html',
      },
    },
    twitter: {
      card: 'summary_large_image',
      title: `${video.title} - YouMind`,
      description: `AI-powered transcript and overview`,
      images: [video.hero_image_url],
    },
    alternates: {
      canonical: `https://youmind.ai/watch/${video.id}`,
    },
  };
}

// 适配器：从 SnipVO 转换为 WatchVideoData 格式
// 暂时注释掉，等有具体的SnipVO类型定义后再实现
// export function adaptSnipToWatchVideoData(snip: SnipVO): WatchVideoData {
//   const videoId = extractVideoIdFromInput(snip.url || "");
//
//   // 实现逻辑...
//
//   return result;
// }
