// 预先储存好数据的静态页面

'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import { useAtom } from 'jotai';
import { ArrowLeft, ArrowRight, BookmarkPlus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/custom/tabs';
import { watchActiveTabAtom } from '../atoms';
import { useWatchTrackActions } from '../hooks';
import type { WatchVideoData } from '../types';
import { AIChatView } from './AIChatView';
import { VideoPlayer } from './VideoPlayer';
import { WatchContentView } from './WatchContentView';

interface WatchPageContainerProps {
  video: WatchVideoData;
  isPreset?: boolean;
  locale?: string;
  pageType?: 'transcript' | 'summary';
}

export function WatchPageContainer({ video, pageType }: WatchPageContainerProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useAtom(watchActiveTabAtom);
  const { trackTabChange } = useWatchTrackActions();
  const [isClient, setIsClient] = useState(false);

  // 确保客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 根据pageType设置默认tab，只在客户端渲染后执行
  useEffect(() => {
    if (!isClient) return;

    if (pageType === 'summary') {
      setActiveTab('summary');
    } else {
      setActiveTab('transcript');
    }
  }, [pageType, setActiveTab, isClient]);

  // 获取第一个作者信息
  const primaryAuthor = video.author[0] || { name: 'Unknown', picture: '' };

  const handleBack = () => {
    router.back();
  };

  const handleSaveToYouMind = () => {
    // TODO: 实现保存到YouMind功能
    console.log('Save to YouMind clicked');
  };

  const transcriptTab = (
    <TabsTrigger
      value="transcript"
      className="notranslate body-strong relative inline-flex h-9 items-center justify-center whitespace-nowrap rounded-none border-b-2 border-b-transparent bg-transparent px-0 py-2 text-caption shadow-none ring-offset-background transition-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none data-[state=active]:border-b-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none"
    >
      Transcript
    </TabsTrigger>
  );
  const summaryTab = (
    <TabsTrigger
      value="summary"
      className="notranslate body-strong relative inline-flex h-9 items-center justify-center whitespace-nowrap rounded-none border-b-2 border-b-transparent bg-transparent px-0 py-2 text-caption shadow-none ring-offset-background transition-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none data-[state=active]:border-b-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none"
    >
      Summary
    </TabsTrigger>
  );

  const handleAddExtension = () => {
    window.open(process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL, '_blank');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航 */}
      <div className="sticky top-0 z-10 bg-white">
        <div className="container pb-2 pt-24">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </div>
      </div>

      <div className="container py-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* 左侧主内容区 */}
          <div className="space-y-6 lg:col-span-2">
            {/* 视频标题和信息 */}
            <div className="space-y-4">
              <h1 className="text-2xl leading-tight text-gray-900 lg:text-3xl">{video.title}</h1>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={primaryAuthor.picture} alt={primaryAuthor.name} />
                    <AvatarFallback>{primaryAuthor.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-gray-900">{primaryAuthor.name}</p>
                    <p className="text-sm text-gray-500">{video.published_at}</p>
                  </div>
                </div>

                <Button
                  onClick={handleSaveToYouMind}
                  className="w-56 rounded-full border border-gray-300 bg-white font-medium text-black hover:from-blue-200 hover:to-purple-200"
                >
                  <BookmarkPlus className="h-4 w-4" />
                  Save to YouMind
                </Button>
              </div>
            </div>

            {/* Tab导航和内容 */}
            {isClient && (
              <Tabs
                value={activeTab}
                className="relative mb-2 w-full px-4 pt-4"
                onValueChange={(value: string) => {
                  const tab = value as 'transcript' | 'summary' | 'ai-chat';
                  setActiveTab(tab);
                  trackTabChange(tab, video.id);
                }}
              >
                <TabsList className="inline-flex h-9 w-full items-center justify-start space-x-8 rounded-none border-b border-border bg-transparent p-0">
                  {pageType === 'transcript' ? transcriptTab : summaryTab}
                  {pageType === 'summary' ? transcriptTab : summaryTab}
                  <TabsTrigger
                    value="ai-chat"
                    className="notranslate body-strong relative inline-flex h-9 items-center justify-center whitespace-nowrap rounded-none border-b-2 border-b-transparent bg-transparent px-0 py-2 text-caption shadow-none ring-offset-background transition-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none data-[state=active]:border-b-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none"
                  >
                    AI Chat
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="transcript" className="mt-6">
                  <WatchContentView videoData={video} contentType="transcript" />
                </TabsContent>

                <TabsContent value="summary" className="mt-6">
                  <WatchContentView videoData={video} contentType="overview" />
                </TabsContent>

                <TabsContent value="ai-chat" className="mt-6">
                  <AIChatView video={video} />
                </TabsContent>
              </Tabs>
            )}
          </div>

          {/* 右侧视频播放器和功能区 */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              {/* 视频播放器 */}
              <VideoPlayer
                thumbnail={video.hero_image_url}
                playUrl={video.play_url}
                title={video.title}
                videoId={video.id}
              />

              {/* 功能提示卡片 */}
              <div className="space-y-4 rounded-3xl bg-gradient-to-br from-blue-50 to-purple-50 p-6 text-center">
                <div className="flex flex-col items-center justify-center gap-1">
                  <div className="text-lg font-medium text-gray-900">
                    Long videos, short on time?
                  </div>
                  <div className="text-lg font-medium text-blue-600">Get the gist instantly.</div>
                </div>

                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-red-500">⏱️</span>
                    Get the gist in seconds
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-blue-500">📝</span>
                    Transcripts in 60+ languages
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-purple-500">🎯</span>
                    Navigate with timestamps
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-green-500">🤖</span>
                    Turn long videos into 3-min blogs
                  </div>
                </div>

                <Button
                  onClick={handleAddExtension}
                  className="w-56 rounded-full border border-gray-300 bg-gradient-to-r font-medium text-black hover:from-blue-200 hover:to-purple-200"
                >
                  <div className="flex items-center justify-center gap-3">
                    <svg viewBox="0 0 24 24" className="h-5 w-5 fill-current text-red-600">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                    </svg>
                    <span className="text-sm font-light text-gray-600">Add to Chrome now</span>
                    <ArrowRight className="h-4 w-4 text-sm font-light text-gray-600" />
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
