// 语言操作按钮

'use client';

import { Select, SelectContent, SelectItem, SelectTrigger } from '@repo/ui/components/ui/select';
import { Languages } from 'lucide-react';

interface WatchLanguageSelectorProps {
  videoId: string;
}

export function WatchLanguageSelector({ videoId }: WatchLanguageSelectorProps) {
  // 暂时模拟数据，实际应该从 video 数据或 API 获取
  const availableLanguages = ['zh-CN']; // 静态页面通常只有一种语言
  const currentLanguage = 'zh-CN';

  // 如果只有一种语言，显示禁用状态的选择器，大小与操作按钮一致
  return (
    <Select value={currentLanguage} disabled={availableLanguages.length <= 1}>
      <SelectTrigger
        className="flex h-6 w-6 items-center justify-center rounded-md border-none p-0 hover:bg-accent hover:text-accent-foreground"
        title="Language"
      >
        <Languages size={16} className="text-muted-foreground" />
        <span className="sr-only">Select language</span>
      </SelectTrigger>
      <SelectContent className="body-strong text-muted-foreground">
        {availableLanguages.map((lang) => (
          <SelectItem key={lang} value={lang}>
            {lang === 'zh-CN' ? '中文' : lang}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
