// YouTube SEO页面 Credits管理器

import { CreditsCheckResult, UserCreditsState } from './types';

export class YouTubeCreditsManager {
  private storageKey = 'youmind_youtube_credits';
  private isClient: boolean;

  constructor() {
    this.isClient = typeof window !== 'undefined';
  }

  // 从URL提取视频ID
  private extractVideoId(url: string): string {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match?.[1] ? match[1] : btoa(url).slice(0, 11);
  }

  // 获取当前Credits状态
  getCreditsState(): UserCreditsState {
    if (!this.isClient) return this.createNewCreditsState();

    const stored = localStorage.getItem(this.storageKey);

    if (stored) {
      try {
        const state = JSON.parse(stored) as UserCreditsState;
        return this.validateAndUpdateState(state);
      } catch (_error) {
        console.warn('Invalid credits data, creating new state');
      }
    }

    return this.createNewCreditsState();
  }

  // 验证并更新状态
  private validateAndUpdateState(state: UserCreditsState): UserCreditsState {
    const today = new Date().toDateString();

    // 检查是否需要重置日使用量
    if (state.dailyUsage.date !== today) {
      state.dailyUsage = {
        date: today,
        totalVideosViewed: 0,
      };
    }

    return state;
  }

  // 创建新的Credits状态
  private createNewCreditsState(): UserCreditsState {
    const now = new Date().toISOString();
    const today = new Date().toDateString();

    const state: UserCreditsState = {
      pageCredits: {
        transcript: {
          pageType: 'transcript',
          videosAccessed: [],
          videosLimit: 3,
          lastResetDate: today,
          firstVisitTime: now,
        },
        summary: {
          pageType: 'summary',
          videosAccessed: [],
          videosLimit: 3,
          lastResetDate: today,
          firstVisitTime: now,
        },
      },
      videoCredits: {},
      dailyUsage: {
        date: today,
        totalVideosViewed: 0,
      },
    };

    this.saveCreditsState(state);
    return state;
  }

  // 检查是否可以访问视频详情页
  canAccessVideoDetail(url: string, pageType: 'transcript' | 'summary'): CreditsCheckResult {
    const state = this.getCreditsState();
    const videoId = this.extractVideoId(url);
    const pageCredits = state.pageCredits[pageType];

    // 如果已经访问过这个视频，允许重新访问
    if (pageCredits.videosAccessed.includes(videoId)) {
      return {
        canAccess: true,
        remainingVideos: pageCredits.videosLimit - pageCredits.videosAccessed.length,
      };
    }

    // 检查页面级限制
    if (pageCredits.videosAccessed.length >= pageCredits.videosLimit) {
      return {
        canAccess: false,
        reason: 'page_limit_exceeded',
        remainingVideos: 0,
        upgradePrompt: `您已查看了 ${pageCredits.videosLimit} 个视频的最大限制。升级以解锁更多视频！`,
      };
    }

    return {
      canAccess: true,
      remainingVideos: pageCredits.videosLimit - pageCredits.videosAccessed.length - 1,
    };
  }

  // 记录视频访问
  recordVideoAccess(url: string, pageType: 'transcript' | 'summary'): boolean {
    const state = this.getCreditsState();
    const videoId = this.extractVideoId(url);
    const pageCredits = state.pageCredits[pageType];

    // 如果已经访问过，更新访问时间
    if (pageCredits.videosAccessed.includes(videoId)) {
      if (state.videoCredits[videoId]) {
        state.videoCredits[videoId].lastAccessTime = new Date().toISOString();
        this.saveCreditsState(state);
      }
      return true;
    }

    // 检查是否超出限制
    if (pageCredits.videosAccessed.length >= pageCredits.videosLimit) {
      return false;
    }

    // 记录新的视频访问
    pageCredits.videosAccessed.push(videoId);

    // 创建视频级Credits
    const now = new Date().toISOString();
    state.videoCredits[videoId] = {
      videoId,
      url,
      firstAccessTime: now,
      lastAccessTime: now,
    };

    // 更新日使用量
    state.dailyUsage.totalVideosViewed++;

    this.saveCreditsState(state);
    return true;
  }

  // 获取使用统计
  getUsageStats(): {
    transcriptVideos: number;
    summaryVideos: number;
    dailyStats: { date: string; videosViewed: number };
  } {
    const state = this.getCreditsState();

    return {
      transcriptVideos: state.pageCredits.transcript.videosAccessed.length,
      summaryVideos: state.pageCredits.summary.videosAccessed.length,
      dailyStats: {
        date: state.dailyUsage.date,
        videosViewed: state.dailyUsage.totalVideosViewed,
      },
    };
  }

  // 保存Credits状态
  private saveCreditsState(state: UserCreditsState): void {
    if (this.isClient) {
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(state));
      } catch (error) {
        console.warn('Failed to save credits state:', error);
      }
    }
  }

  // 重置Credits（调试用）
  resetCredits(): void {
    if (this.isClient) {
      localStorage.removeItem(this.storageKey);
    }
  }
}
