'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/ui/components/ui/accordion';
import clsx from 'clsx';
import { useEffect, useState } from 'react';

const qaList = [
  {
    question: 'How can I use YouMind?',
    answer:
      'You can collect information using the YouMind browser extension or mobile app, and engage in deep thinking and creation by visiting youmind.ai.',
  },
  {
    question: 'Is YouMind free?',
    answer:
      'To provide a higher quality service, YouMind is not free, and it requires payment for advanced features. However, we offer a free trial so you can fully experience the power of YouMind before paying.',
  },
  {
    question: 'How to pay for YouMind?',
    answer:
      'You can make payments through Stripe using credit cards, mobile payments, and other methods. You can pay monthly or annually.',
  },
];

const Questions = () => {
  const [selectedQA, setSelectedQA] = useState<string | undefined>(undefined);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    setSelectedQA(qaList[0]?.question);
  }, []);

  return (
    <div className="mb-[100px] flex flex-col items-center">
      <h2 className="mb-[28px] font-sans-title text-[32px] leading-[44px] md:mb-[28px] md:text-[42px] md:leading-[58px]">
        Frequently Asked Questions
      </h2>
      <div className="hidden md:block">
        {qaList.map((item, index) => (
          <QaItem key={index} item={item} index={index} />
        ))}
      </div>

      <div className="block w-full md:hidden">
        {isMounted && (
          <Accordion
            type="single"
            collapsible
            defaultValue={selectedQA}
            onValueChange={(value) => value && setSelectedQA(value)}
            className="w-full"
          >
            {qaList.map((qa) => (
              <AccordionItem
                key={qa.question}
                value={qa.question}
                className="border-b border-muted"
              >
                <AccordionTrigger className="w-full py-4 hover:no-underline">
                  <div className="flex w-full items-center">
                    <div
                      className={`w-full text-left text-xl transition-all duration-300 ${
                        selectedQA === qa.question
                          ? 'font-semibold text-foreground'
                          : 'font-normal text-muted-foreground'
                      }`}
                    >
                      {qa.question}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <p className="mb-3 text-base text-muted-foreground">{qa.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
};

const QaItem = ({ item }: { item: (typeof qaList)[number]; index: number }) => {
  return (
    <div className="flex max-w-[800px] border-muted py-[32px] [&:not(:last-child)]:border-b">
      <div className="w-full flex-grow">
        <h3 className="text-[24px] font-semibold leading-[38px]">{item.question}</h3>
        <p className={clsx('mt-2 text-[16px] leading-[28px] text-muted-foreground')}>
          {item.answer}
        </p>
      </div>
    </div>
  );
};

export default Questions;
