'use client';

import { But<PERSON> } from '@repo/ui/components/ui/button';
import { Toggle } from '@repo/ui/components/ui/toggle';
import { useAtom } from 'jotai';
import { Check, Copy, Download } from 'lucide-react';
import { useState } from 'react';
import { LineIcon } from '@/components/icon/line';
import { ParagraphIcon } from '@/components/icon/paragraph';
import { cn } from '@/lib/utils';
import { watchDisplayLineByLineAtom, watchHaveViewedParagraphAtom } from '../atoms';
import {
  useWatchMultiLanguageTranscript,
  useWatchOverview,
  useWatchTrackActions,
  useWatchTranscriptExport,
} from '../hooks';
import { WatchVideoData } from '../types';
import { ContentRenderer } from './ContentRenderer';
import { WatchLanguageSelector } from './WatchLanguageSelector';

interface WatchContentViewProps {
  videoData: WatchVideoData;
  contentType: 'transcript' | 'overview';
}

export function WatchContentView({ videoData, contentType }: WatchContentViewProps) {
  const [displayLineByLine, setDisplayLineByLine] = useAtom(watchDisplayLineByLineAtom);
  const [haveViewedParagraph, setHaveViewedParagraph] = useAtom(watchHaveViewedParagraphAtom);
  const [copied, setCopied] = useState(false);
  const { trackViewModeChange, trackTranscriptAction } = useWatchTrackActions();
  const { handleExport } = useWatchTranscriptExport();

  // 使用优化后的格式选择逻辑
  // 注：这里仍使用原名称保持向后兼容，实际使用的是格式选择逻辑
  const {
    currentContent: transcriptContent,
    currentTranscript,
    subtitleTranscript, // Line View 使用的解析内容
    subtitleFormattedTranscript, // Chapter View 使用的解析内容
    subtitleContent, // Line View 使用的原始内容对象
    subtitleFormattedContent, // Chapter View 使用的原始内容对象
    hasMultipleFormats, // 是否有多种格式可选（用于UI控制）
  } = useWatchMultiLanguageTranscript(contentType === 'transcript' ? videoData : null);

  const { overviewContent } = useWatchOverview(contentType === 'overview' ? videoData : null);

  // 确定当前要渲染的内容
  const currentContent = contentType === 'transcript' ? transcriptContent : overviewContent;

  if (!currentContent) {
    return (
      <div className="flex flex-col items-center py-8 text-center">
        <div className="text-gray-500">
          {contentType === 'transcript' ? 'No transcript available' : 'No overview available'}
        </div>
      </div>
    );
  }

  // UI状态控制
  const isTranscriptContent =
    currentContent.format === 'subtitle' ||
    currentContent.format === 'subtitle-formatted' ||
    subtitleContent ||
    subtitleFormattedContent;

  const showViewToggle = isTranscriptContent && hasMultipleFormats; // 只有多种格式时才显示切换
  const showLanguageSelector = false; // 当前业务下不显示语言选择器（因为只有一种语言）

  // 内容操作
  const getContentToCopy = () => {
    if (isTranscriptContent) {
      // 转录格式：根据当前视图模式选择对应的转录内容
      let transcriptToCopy = currentTranscript;

      if (subtitleContent || subtitleFormattedContent) {
        // 如果有分别的数据，根据视图模式选择
        transcriptToCopy = displayLineByLine
          ? subtitleTranscript.length > 0
            ? subtitleTranscript
            : subtitleFormattedTranscript
          : subtitleFormattedTranscript.length > 0
            ? subtitleFormattedTranscript
            : subtitleTranscript;
      }

      return transcriptToCopy.map((item) => `${item.timestamp}\n${item.content}`).join('\n\n');
    } else {
      // llm-output 格式：使用原始 plain 内容
      return currentContent.plain;
    }
  };

  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(getContentToCopy());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      trackTranscriptAction('copy', videoData.id);
    } catch (err) {
      console.error('Failed to copy content:', err);
    }
  };

  const handleDownloadContent = () => {
    if (isTranscriptContent) {
      // 转录格式：根据当前视图模式选择对应的转录内容并使用转录导出功能
      let transcriptToExport = currentTranscript;

      if (subtitleContent || subtitleFormattedContent) {
        // 如果有分别的数据，根据视图模式选择
        transcriptToExport = displayLineByLine
          ? subtitleTranscript.length > 0
            ? subtitleTranscript
            : subtitleFormattedTranscript
          : subtitleFormattedTranscript.length > 0
            ? subtitleFormattedTranscript
            : subtitleTranscript;
      }

      handleExport(transcriptToExport, videoData.id);
    } else {
      // llm-output 格式：直接下载 plain 内容
      const blob = new Blob([currentContent.plain], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${contentType}-${videoData.id}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
    trackTranscriptAction('export', videoData.id);
  };

  return (
    <div className="space-y-4">
      {/* 顶部工具栏 */}
      <div className="sticky top-0 z-40 flex w-full items-center justify-between border-border/50 bg-card/80 px-2 py-2 backdrop-blur-sm">
        {/* 左侧：视图切换按钮（仅在转录内容时显示） */}
        <div className="flex items-center gap-x-2">
          {contentType !== 'overview' && showViewToggle && (
            <>
              <Toggle
                className="h-8 gap-2 rounded-md px-3 text-sm text-muted-foreground"
                pressed={displayLineByLine}
                onPressedChange={() => {
                  setDisplayLineByLine(true);
                  trackViewModeChange('line', videoData.id);
                }}
              >
                <LineIcon size={16} />
                Line view
              </Toggle>

              <Toggle
                className={cn(
                  'h-8 gap-2 rounded-md px-3 text-sm text-muted-foreground',
                  !haveViewedParagraph && 'animate-pulse',
                )}
                pressed={!displayLineByLine}
                onPressedChange={() => {
                  setDisplayLineByLine(false);
                  setHaveViewedParagraph(true);
                  trackViewModeChange('chapter', videoData.id);
                }}
              >
                <ParagraphIcon size={16} />
                Chapter view
              </Toggle>
            </>
          )}
        </div>

        {/* 右侧：语言选择器和操作按钮 */}
        <div className="flex items-center gap-x-2">
          {/* 语言选择器（仅在多语言转录时显示） */}
          {showLanguageSelector && <WatchLanguageSelector videoId={videoData.id} />}

          {/* 操作按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopyContent}
            className="h-6 w-6 p-0"
            title={copied ? 'Copied!' : 'Copy content'}
          >
            {copied ? <Check size={16} /> : <Copy size={16} />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDownloadContent}
            className="h-6 w-6 p-0"
            title="Export content"
          >
            <Download size={16} />
          </Button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="h-[60vh] max-h-[600px] min-h-[400px] overflow-y-auto overflow-x-hidden">
        <ContentRenderer
          content={currentContent}
          transcriptItems={currentTranscript}
          subtitleTranscript={subtitleTranscript}
          subtitleFormattedTranscript={subtitleFormattedTranscript}
          currentSubtitleContent={subtitleContent}
          currentSubtitleFormattedContent={subtitleFormattedContent}
          videoId={videoData.id}
        />
      </div>
    </div>
  );
}
