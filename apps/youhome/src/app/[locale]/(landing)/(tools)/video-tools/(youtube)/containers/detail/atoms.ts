import { atom } from 'jotai';

import { WatchVideoData } from './types';

// === 视图控制状态 ===

// 转录视图模式：true=line view, false=chapter view
export const watchDisplayLineByLineAtom = atom<boolean>(true);

// 是否已经查看过段落视图（用于提示动画）
export const watchHaveViewedParagraphAtom = atom<boolean>(false);

// 当前活跃的 tab
export const watchActiveTabAtom = atom<'transcript' | 'summary' | 'ai-chat'>('transcript');

// === 数据状态 ===

// 视频数据状态
export const watchVideoDataAtom = atom<WatchVideoData | null>(null);

// 加载状态
export const watchIsLoadingAtom = atom<boolean>(false);

// 错误状态
export const watchErrorAtom = atom<string | null>(null);

// === 向后兼容的状态（已优化为格式选择） ===

/**
 * @deprecated 保留用于向后兼容
 * 现在主要用于标识视频的单一语言，不再用于语言切换
 * 实际值由视频内容决定（如 'en', 'zh-CN' 等）
 */
export const watchSelectedLanguageAtom = atom<string>('en');

/**
 * @deprecated 保留用于向后兼容
 * 现在永远只包含一个元素（视频的主要语言）
 * 格式：[{ code: string, name: string }]
 */
export const watchAvailableLanguagesAtom = atom<Array<{ code: string; name: string }>>([]);
