import { YouTubePageConfig } from '../../config';
import FeatDescBox from '../FeatDescBox';

interface FeatDescBoxesProps {
  config: YouTubePageConfig;
  className?: string;
}

const FeatDescBoxes = ({ config, className = '' }: FeatDescBoxesProps) => {
  return (
    <div className={`mb-[100px] px-8 lg:px-10 ${className}`}>
      {/* 特色描述框標題 */}
      <h2 className="mb-12 text-center font-sans-title text-3xl md:mb-16 md:text-4xl lg:text-[42px]">
        {config.featDescBoxes.title}
      </h2>

      {/* 特色描述框內容 */}
      <div className="grid grid-cols-1 gap-[60px] md:grid-cols-2 md:gap-[80px]">
        {config.featDescBoxes.boxes.map((box, index) => (
          <FeatDescBox key={index} {...box} isMobile={false} />
        ))}
      </div>
    </div>
  );
};

export default FeatDescBoxes;
