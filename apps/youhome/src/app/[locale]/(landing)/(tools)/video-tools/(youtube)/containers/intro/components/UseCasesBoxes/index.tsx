import UseCasesBox from '../UseCasesBox';

interface UseCasesProps {
  className?: string;
}

export default function UseCases({ className = '' }: UseCasesProps) {
  const useCases = [
    {
      image: 'https://cdn.gooo.ai/assets/multi-mode.png',
      title: 'Students & Researchers',
      description:
        'Take notes from educational videos, create study materials, and reference lectures',
    },
    {
      image: 'https://cdn.gooo.ai/assets/multi-mode.png',
      title: 'Content Creators',
      description: 'Generate subtitles, create blog posts from videos, and repurpose content',
    },
    {
      image: 'https://cdn.gooo.ai/assets/multi-mode.png',
      title: 'Business Professionals',
      description: 'Transcribe webinars, training videos, and create meeting summaries',
    },
  ];

  return (
    <div className={`${className}`}>
      <div className="mb-12 text-center">
        <h2 className="mb-12 text-center font-sans-title text-3xl md:mb-16 md:text-4xl lg:text-[42px]">
          Use Cases
        </h2>
      </div>

      <div className="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {useCases.map((useCase, index) => (
          <UseCasesBox
            key={index}
            image={useCase.image}
            title={useCase.title}
            description={useCase.description}
          />
        ))}
      </div>
    </div>
  );
}
