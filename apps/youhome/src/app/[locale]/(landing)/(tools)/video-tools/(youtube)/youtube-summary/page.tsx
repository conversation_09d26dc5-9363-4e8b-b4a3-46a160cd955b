import { Metadata } from 'next';

import YouTubePageTemplate from '../containers/intro/YouTubePageTemplate';
import { summaryConfig } from './config';

export default async function YouTubeSummaryPage() {
  return <YouTubePageTemplate config={summaryConfig} enableCredits={true} />;
}

export const metadata: Metadata = {
  title: summaryConfig.metaTitle,
  robots: 'index,follow',
  alternates: {
    canonical: summaryConfig.canonical,
  },
  other: {
    googlebot: 'index,follow',
  },
};
