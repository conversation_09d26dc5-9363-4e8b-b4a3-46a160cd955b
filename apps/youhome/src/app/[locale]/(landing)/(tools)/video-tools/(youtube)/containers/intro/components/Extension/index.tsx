'use client';

import { Button } from '@repo/ui/components/ui/button';
import { ArrowRight } from 'lucide-react';

interface ExtensionProps {
  className?: string;
}

export default function Extension({ className = '' }: ExtensionProps) {
  const handleOpenExtension = () => {
    // 安全检查：确保在客户端环境中才调用window.open
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL) {
      window.open(process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL, '_blank');
    }
  };

  return (
    <div className={`mb-[100px] rounded-2xl p-8 md:p-12 ${className}`}>
      <div className="mx-auto max-w-4xl text-center">
        <p className="mb-4 text-xl md:text-xl" style={{ color: '#666666' }}>
          Tired of copying links ? Get a browser extension instead !
        </p>

        <Button
          size="lg"
          className="hover:bg-gray-80 rounded-3xl bg-gray-50 px-6 py-3 text-black shadow-sm"
          onClick={handleOpenExtension}
        >
          <div className="flex items-center gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-sm">
              <svg viewBox="0 0 24 24" className="h-6 w-6 fill-current text-red-600">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
              </svg>
            </div>
            <span className="text-base font-normal">
              Add to browser for instant video transcripts
            </span>
            <ArrowRight className="h-5 w-5" aria-hidden="true" />
          </div>
        </Button>
      </div>
    </div>
  );
}
