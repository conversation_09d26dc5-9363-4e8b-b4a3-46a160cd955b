'use client';

import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';

import { YouTubeCreditsManager } from '../manager';
import { PageType } from '../types';

interface StatusBarProps {
  pageType: PageType;
  currentVideoUrl?: string;
  showVideoLevelStats?: boolean;
  className?: string;
}

interface Usage {
  transcriptVideos: number;
  summaryVideos: number;
}

export default function StatusBar({ pageType, currentVideoUrl, className = '' }: StatusBarProps) {
  const t = useTranslations('Credits');
  const [usage, setUsage] = useState<Usage>({
    transcriptVideos: 0,
    summaryVideos: 0,
  });
  const [manager, setManager] = useState<YouTubeCreditsManager | null>(null);
  const [isManagerReady, setIsManagerReady] = useState(false);

  // 在客户端初始化 manager
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const creditsManager = new YouTubeCreditsManager();
        setManager(creditsManager);
        setIsManagerReady(true);
      } catch (error) {
        console.warn('Failed to initialize credits manager:', error);
        setIsManagerReady(true);
      }
    }
  }, []);

  const updateStats = useCallback(() => {
    if (!manager || !isManagerReady) return;

    const stats = manager.getUsageStats();
    setUsage({
      transcriptVideos: stats.transcriptVideos,
      summaryVideos: stats.summaryVideos,
    });
  }, [manager, isManagerReady, currentVideoUrl]);

  useEffect(() => {
    if (isManagerReady) {
      updateStats();
    }
  }, [updateStats, isManagerReady]);

  const isPageLimitReached =
    pageType === 'transcript' ? usage.transcriptVideos >= 3 : usage.summaryVideos >= 3;

  // 如果manager还未准备就绪，显示加载状态或返回null
  if (!isManagerReady) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between bg-blue-50 px-4 py-3 ${className}`}>
      <div className="flex items-center gap-4">
        {/* 页面级统计 */}
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
            <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="text-sm">
            <span>
              {t('videoViews')}:{' '}
              {pageType === 'transcript' ? usage.transcriptVideos : usage.summaryVideos}
              /3
            </span>
            {isPageLimitReached && <span className="text-xs"> • {t('limitReached')}</span>}
          </div>
        </div>
      </div>

      {/* 升级提示 */}
      {isPageLimitReached && (
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 rounded-lg bg-blue-100 px-3 py-2">
            <svg
              className="h-4 w-4 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
              />
            </svg>
            <div>
              <p className="text-sm font-medium text-blue-800">{t('unlockUnlimited')}</p>
              <p className="text-xs text-blue-600">{t('upgradeDescription')}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
