// 预设视频数据管理文件
// 管理Examples展示和预设的静态视频数据

import { WatchVideoData } from './containers/detail/types';

/**
 * 示例视频数据（用于Examples组件展示）
 *
 * 这些视频会在营销页面的Examples部分展示，吸引用户点击体验。
 * 包含预设视频和非预设视频的混合。
 */
export interface ExampleVideo {
  id: string;
  title: string;
  thumbnail: string;
  url: string;
}

export const EXAMPLE_VIDEOS: ExampleVideo[] = [
  {
    id: 'e-P5IFTqB98',
    title: 'Black Holes Explained - From Birth to Death',
    thumbnail: 'https://img.youtube.com/vi/e-P5IFTqB98/maxresdefault.jpg',
    url: 'https://www.youtube.com/watch?v=e-P5IFTqB98',
  },
  {
    id: '1PQ_pQI6be8',
    title: 'Apple WWDC 2025 keynote in 28 minutes',
    thumbnail: 'https://img.youtube.com/vi/1PQ_pQI6be8/maxresdefault.jpg',
    url: 'https://www.youtube.com/watch?v=1PQ_pQI6be8',
  },
  {
    id: 'id4YRO7G0wE',
    title: 'The AI Revolution Is Underhyped | Eric Schmidt | TED',
    thumbnail: 'https://img.youtube.com/vi/id4YRO7G0wE/hqdefault.jpg',
    url: 'https://www.youtube.com/watch?v=id4YRO7G0wE',
  },
  {
    id: 'UF8uR6Z6KLc',
    title: "Steve Jobs' 2005 Stanford Commencement Address",
    thumbnail: 'https://img.youtube.com/vi/UF8uR6Z6KLc/hqdefault.jpg',
    url: 'https://www.youtube.com/watch?v=UF8uR6Z6KLc',
  },
];

/**
 * 预设视频数据
 *
 * 这些视频会被预先缓存并生成静态页面，用于SEO优化和快速展示。
 * 主要用于：
 * 1. 示例展示 - 在 Examples 组件中展示
 * 2. SEO优化 - 预生成静态页面
 * 3. 快速加载 - 无需实时API调用
 *
 * 添加新视频时：
 * 1. 在此文件中添加视频数据
 * 2. 确保 videoId 准确无误
 * 3. 提供完整的 transcript 和 summary
 * 4. 运行 generateStaticParams 会自动包含新视频
 */
export const PRESET_VIDEOS: Record<string, WatchVideoData> = {
  'e-P5IFTqB98': {
    id: 'e-P5IFTqB98',
    title: 'Black Holes Explained - From Birth to Death',
    author: [
      {
        name: 'Kurzgesagt - In a Nutshell',
        picture:
          'https://cdn.gooo.ai/web-images/0cabbca82a1687dcbfbd5377c1b3ca7e75ecb15db36263d9f45c289d483211c8',
      },
    ],
    views: '1.2M',
    published_at: 'Dec 15, 2015',
    created_at: '2025-07-15T07:13:04.757Z',
    hero_image_url: 'https://img.youtube.com/vi/e-P5IFTqB98/maxresdefault.jpg',
    play_url: 'https://www.youtube.com/watch?v=e-P5IFTqB98',
    transcript: {
      contents: [
        {
          id: 'e-P5IFTqB98-subtitle-formatted',
          block_id: 'e-P5IFTqB98-block-formatted',
          plain:
            "[Black holes are one of the strangest things in existence. They don't seem to make any sense at all. Where do they come from, and what happens if you fall into one?](#00:00)\n[Stars are incredibly massive collections of mostly hydrogen atoms that collapsed from enormous gas cloud under their own gravity. In their core, nuclear fusion crushes hydrogen atoms into helium, releasing a tremendous amount of energy. This energy, in the form of radiation, pushes against gravity, maintaining a delicate balance between the two forces. As long as there is fusion in the core, a star remains stable enough.](#00:15)\n[But for stars with way more mass then our own sun, the heat and pressure at the core allow them to fuse heavier elements until they reach iron. Unlike all the elements that went before, the fusion process that creates iron doesn't generate any energy. Iron builds up at the center of the star until it reaches a critical amount, and the balance between radiation and gravity is suddenly broken. The core collapses.](#00:41)\n[Within a fraction of a second, the star implodes, moving at about the quarter of the speed of light, feeding even more mass into the core. It's at this very moment that all the heavier elements in the universe are created, as the star dies, in a super nova explosion. This produces either a neutron star, or if the star is massive enough, the entire mass of the core collapses into a black hole.](#01:05)\n[If you looked at a black hole, what you'd really be seeing is the event horizon. Anything that crosses the event horizon needs to be travelling faster than the speed of light to escape.](#01:29)\n[In other words, it's impossible, so we just see a black sphere reflecting nothing. But if the event horizon is the black part, what is the \"hole\" part of the black hole?](#01:40)\n[The singularity. We're not sure what it is exactly. A singularity may be indefinitely dense, meaning all its mass is concentrated into a single point in space, with no surface or volume, or something completely different. Right now, we just don't know. It's like a \"dividing by zero\" error.](#01:52)\n[By the way, black holes do not suck things up like a vacuum cleaner. If we were to swap the sun for an equally massive black hole, nothing much would change for Earth, except that we would freeze to death, of course. What would happen to you if you fell into a black hole?](#02:11)\n[The experience of time is different around black holes. From the outside, you seem to slow down as you approach the event horizon, so time passes slower for you. At some point, you would appear to freeze in time, slowly turn red, and disappear. While from your perspective, you can watch the rest of the universe in fast forward, kind of like seeing into the future.](#02:22)\n[Right now, we don't know what happens next, but we think it could be one of two things: One, you die a quick death. A black hole curves space so much that once you cross the event horizon, there is only one possible direction. You can take—literally—inside the event horizon, you can only go in one direction. It's like being in a really tight alley that closes behind you after each step.](#02:45)\n[The mass of a black hole is so concentrated that at some point, even tiny distances of a few centimeters would mean that gravity acts with millions of times more force on different parts of your body. Your cells get torn apart, as your body stretches more and more, until you are a hot stream of plasma, one atom wide.](#03:07)\n[Two, you die a very quick death. Very soon after you cross the event horizon, you would hit a firewall and be terminated in an instant. Neither of these options are particularly pleasant.](#03:27)\n[How soon you would die depends on the mass of the black hole. A smaller black hole would kill you before you even enter its event horizon, while you probably could travel inside a super size massive black hole for quite a while. As a rule of thumb, the further away from the singularity you are, the longer you live.](#03:34)\n[Black holes come in different sizes. There are stellar mass black holes, with a few times the mass of the sun, and the diameter of an asteroid. And then there are the super massive black holes, which are found at the heart of every galaxy, and have been feeding for billions of years. Currently, the largest super massive black hole known is S5 0014+81, 40 billion times the mass of our sun. It is 236.7 billion kilometers in diameter, which is 47 times the distance from the sun to Pluto.](#03:54)\n[As powerful as black holes are, they will eventually evaporate through a process called Hawking radiation. To understand how this works, we have to look at empty space. Empty space is not really empty, but filled with virtual particles popping into existence and annihilating each other again. When this happens right on the edge of a black hole, one of the virtual particles will be drawn into the black hole, and the other will escape and become a real particle, so the black hole is losing energy.](#04:26)\n[This happens incredibly slowly at first, and gets faster as the black hole becomes smaller. When it arrives at the mass of a large asteroid, it's radiating at room temperature. When it has the mass of a mountain, it radiates with about the heat of our sun. And in the last second of its life, the black hole radiates away with the energy of billions of nuclear bombs in a huge explosion.](#04:50)\n[But this process is incredibly slow. The biggest black holes we know might take up a googol year to evaporate. This is so long that when the last black hole radiates away, nobody will be around to witness it. The universe will have become uninhabitable long before then.](#05:13)\n[This is not the end of our story; there are loads more interesting ideas about black holes. We'll explore them in part 2.](#05:29)",
          format: 'subtitle-formatted',
          raw: 'raw-transcript-formatted',
          language: 'en',
          status: 'completed',
        },
        {
          id: 'e-P5IFTqB98-subtitle',
          block_id: 'e-P5IFTqB98-block-subtitle',
          raw: 'raw-transcript-subtitle',
          plain:
            "0:00: Black holes are one of the strangest things in existence.\n0:04: They don't seem to make any sense at all.\n0:06: Where do they come from...\n0:07: ...and what happens if you fall into one?\n0:16: Stars are incredibly massive collections of mostly hydrogen atoms\n0:20: that collapsed from enormous gas cloud under their own gravity.\n0:23: In their core, nuclear fusion crushes hydrogen atoms into helium\n0:27: releasing a tremendous amount of energy\n0:29: This energy, in the form of radiation,\n0:32: pushes against gravity,\n0:33: maintaining a delicate balance between the two forces.\n0:37: As long as there is fusion in the core,\n0:39: a star remains stable enough.\n0:41: But for stars with way more mass then our own sun\n0:44: the heat and pressure at the core allow them to fuse heavier elements\n0:48: until they reach iron.\n0:50: Unlike all the elements that went before,\n0:52: the fusion process that creates iron\n0:55: doesn't generate any energy.\n0:57: Iron builds up at the center of the star\n0:59: until it reaches a critical amount\n1:01: and the balance between radiation and gravity is suddenly broken.\n1:04: The core collapses.\n1:06: Within a fraction of a second,\n1:08: the star implodes.\n1:10: Moving at about the quarter of the speed of light,\n1:12: feeding even more mass into the core.\n1:14: It's at this very moment that all the heavier elements in the universe are created,\n1:19: as the star dies, in a super nova explosion.\n1:22: This produces either a neutron star,\n1:24: or if the star is massive enough,\n1:26: the entire mass of the core collapses into a black hole.\n1:30: If you looked at a black hole,\n1:32: what you'd really be seeing is the event horizon.\n1:35: Anything that crosses the event horizon\n1:37: needs to be travelling faster than the speed of light to escape.\n1:40: In other words, its impossible.\n1:43: So we just see a black sphere\n1:45: reflecting nothing.\n1:46: But if the event horizon is the black part,\n1:49: what is the \"hole\" part of the black hole?\n1:52: The singularity.\n1:54: We're not sure what it is exactly.\n1:56: A singularity may be indefinitely dense,\n1:59: meaning all its mass is concentrated into a single point in space,\n2:02: with no surface or volume,\n2:04: or something completely different.\n2:06: Right now, we just don't know.\n2:08: its like a \"dividing by zero\"error.\n2:11: By the way, black holes do not suck things up like a vacuum cleaner,\n2:15: If we were to swap the sun for an equally massive black hole,\n2:18: nothing much would change for earth,\n2:19: except that we would freeze to death, of course.\n2:22: what would happen to you if you fell into a black hole?\n2:25: The experience of time is different around black holes,\n2:28: from the outside,\n2:29: you seem to slow down as you approach the event horizon,\n2:32: so time passes slower for you.\n2:34: at some point, you would appear to freeze in time,\n2:37: slowly turn red,\n2:38: and disapear.\n2:39: While from your perspective,\n2:41: you can watch the rest of the universe in fast forward,\n2:43: kind of like seeing into the future.\n2:45: Right now, we don't know what happens next,\n2:47: but we think it could be one of two things:\n2:50: One, you die a quick death.\n2:52: A black hole curves space so much,\n2:55: that once you cross the event horizon,\n2:56: there is only one possible direction.\n2:58: you can take this - literally - inside the event horizon,\n3:01: you can only go in one direction.\n3:04: Its like being in a really tight alley that closes behind you after each step.\n3:09: The mass of a black hole is so concentrated,\n3:11: at some point even tiny distances of a few centimeters,\n3:14: would means that gravity acts with millions of times more force on different parts of your body.\n3:19: Your cells get torn apart,\n3:21: as your body stretches more and more,\n3:23: until you are a hot stream of plasma,\n3:25: one atom wide.\n3:27: Two, you die a very quick death.\n3:29: Very soon after you cross the event horizon,\n3:31: you would hit a firewall and be terminated in an instant.\n3:34: Neither of these options are particularly pleasant.\n3:38: How soon you would die depends on the mass of the black hole.\n3:41: A smaller black hole would kill you before you even enter its event horizon,\n3:45: while you probably could travel inside a super size massive black hole for quite a while.\n3:49: As a rule of thumb,\n3:50: the further away from the singularity you are,\n3:52: the longer you live.\n3:54: Black holes come in different sizes.\n3:56: There are stellar mass black holes,\n3:58: with a few times the mass of sun,\n3:60: and the diameter of an asteroid.\n4:02: And then there are the super massive black holes,\n4:04: which are found at the heart of every galaxy,\n4:06: and have been feeding for billions of years.\n4:09: Currently, the largest super massive black hole known,\n4:12: is S5 0014+81.\n4:15: 40 billion times the mass of our sun.\n4:18: It is 236.7 billion kilometers in diameter,\n4:22: which is 47 times the distance from the sun to Pluto.\n4:26: As powerful as black holes are,\n4:28: they will eventually evaporate through a process called Hawking radiation.\n4:32: To understand how this works,\n4:34: we have to look at empty space.\n4:36: Empty space is not really empty,\n4:38: but filled with virtual particles popping into existence\n4:40: and annihilating each other again.\n4:42: When this happens right on the edge of a black hole,\n4:44: one of the virtual particles will be drawn into the black hole,\n4:47: and the other will escape and become a real particle.\n4:50: So the black hole is losing energy.\n4:53: This happens incredibly slowly at first,\n4:55: and gets faster as the black hole becomes smaller.\n4:58: When it arrives at the mass of a large asteroid,\n5:00: its radiating at room temperature.\n5:02: When it has the mass of a mountain,\n5:04: it radiates with about the heat of our sun.\n5:06: and in the last second of its life,\n5:08: the black hole radiates away with the energy of billions of nuclear bombs in a huge explosion.\n5:13: But this process is incredibly slow,\n5:15: The biggest black holes we know,\n5:17: might take up a googol year to evaporate.\n5:20: This is so long that when the last black hole radiates away,\n5:23: nobody will be around to witness it.\n5:25: The universe will have become uninhabitable,\n5:28: long before then.\n5:29: This is not the end of our story,\n5:31: there are loads more interesting ideas about black holes,\n5:33: we'll explore them in part 2.",
          format: 'subtitle',
          language: 'en',
          status: 'completed',
        },
      ],
      current_content_id: 'e-P5IFTqB98-subtitle-formatted',
      type: 'transcript',
    },
    overview: {
      contents: [
        {
          id: 'e-P5IFTqB98-overview',
          block_id: 'e-P5IFTqB98-overview-block',
          raw: 'overview-content',
          plain:
            'Black holes are mysterious cosmic entities formed from massive stars that collapse under gravity. They consist of an event horizon and a singularity, with unique effects on time and space. Black holes vary in size and eventually evaporate through Hawking radiation, leading to fascinating implications for the universe.\n\n### Black Holes Overview\n\n#### Formation of Black Holes\n\n* Massive stars collapse after exhausting nuclear fusion, leading to supernovae. [citation-01:10]\n\n* The core can either become a neutron star or collapse into a black hole if sufficiently massive. [citation-01:26]\n\n#### Structure of Black Holes\n\n* The event horizon marks the boundary beyond which escape is impossible; it appears as a black sphere. [citation-01:45]\n\n* The singularity at the center may be infinitely dense, though its nature remains uncertain. [citation-02:04]\n\n#### Effects of Falling into a Black Hole\n\n* Approaching the event horizon alters time perception, causing external observers to see objects freeze and fade away. [citation-02:38]\n\n* Two potential outcomes exist upon crossing the event horizon: being torn apart by tidal forces or encountering a firewall instantaneously ending life. [citation-03:31]\n\n#### Types and Sizes of Black Holes\n\n* Stellar mass black holes possess several solar masses, while supermassive black holes reside at galactic centers and can reach billions of solar masses. [citation-04:06]\n\n* S5 0014+81 is currently the largest known supermassive black hole, significantly larger than our sun. [citation-04:15]\n\n#### Evaporation of Black Holes\n\n* Through Hawking radiation, black holes lose energy over time, eventually leading to their evaporation in extremely long timescales. [citation-04:28]\n\n* This process culminates in massive explosions before total evaporation occurs, but it takes an astronomically long time for significant-sized black holes. [citation-05:08]',
          format: 'llm-output',
          language: 'en',
          status: 'completed',
        },
      ],
      current_content_id: 'e-P5IFTqB98-overview',
      type: 'overview',
    },
  },
  '1PQ_pQI6be8': {
    id: '1PQ_pQI6be8',
    title: 'Apple WWDC 2025 keynote in 28 minutes',
    author: [
      {
        name: 'The Verge',
        picture:
          'https://cdn.gooo.ai/web-images/a2e8688f0cdf44e831f08b12f70af2e44f590b5e2f9f16b3fd939b8379e7d3e2',
      },
    ],
    views: '2.1M',
    published_at: 'Jun 9, 2025',
    created_at: '2025-06-09T22:28:06.000Z',
    hero_image_url:
      'https://cdn.gooo.ai/web-images/c4198061cf0afc7a2f77b2a8cee250b69475943b0439c9bdf1467acd82075816',
    play_url: 'https://www.youtube.com/watch?v=1PQ_pQI6be8',
    transcript: {
      contents: [
        {
          id: '0197ee94-a861-757c-afdb-22f9235bba77',
          block_id: '0197ee93-e8c2-7cdf-80b3-ae4286461759',
          plain:
            "[Good morning, and welcome to WWDC. Here's Craig. First is Apple Intelligence. We're opening up access for any app to tap directly into the on-device large language model at the core of Apple Intelligence with a new Foundation Models framework. This gives developers direct access to intelligence that's powerful, fast, built with privacy, and available even when you're offline.](#00:00)\n[For example, if you're getting ready for an exam, an app like Kahoot! can create a personalized quiz from your notes to make studying more engaging. And because it uses on-device models, this happens without cloud API costs.](#00:27)\n[And for the first time, we're introducing a universal design across our platforms, and it starts with an entirely new expressive material we call liquid glass. It transforms depending on your content or even your context and brings more clarity to navigation and controls. It beautifully refracts light and dynamically reacts to your movement with specular highlights.](#00:41)\n[Elements once considered for rectangular displays have been redesigned to fit perfectly concentric with the rounded corners of the hardware. The lock screen has been updated with the time and controls now made of liquid glass with this beautiful glass edge and a new sense of responsiveness. Whether you use dark mode or one of our entirely new styles, like our all clear look.](#01:04)\n[I'll switch to my photo shuffle wallpaper. And watch as I switch images. The time now fluidly adapts to the available space. Its San Francisco typeface has been uniquely crafted to dynamically scale the weight width.](#01:29)\n[...height of each numeral in order to nestle into the scene. It also keeps the best part of my photo and view as new information comes in, like a message from a friend or an email from a colleague. With advanced computer vision techniques running on the neural engine, we generate a spatial scene from your 2D photos creating a delightful 3D effect, bringing your favorite memories to life as you move your iPhone in your hand.](#01:46)\n[Next, let's talk camera. A simplified and streamlined design elevates the two capture modes that you use most: photo and video. Just swipe your finger left or right to reveal additional modes like cinematic mode, portrait mode, and a simple swipe up lets you easily reach all your settings like aspect ratio and timers. Photos now features separate tabs for library and for collections, where you'll find your favorites, your albums, and easy access to search.](#02:13)\n[Webpages now flow edge to edge to the very bottom of the screen, enabling you to see more of the page. The tab bar has been redesigned to float above the webpage and surfaces frequently used actions like search and refresh. When on a call, your most important controls now float on the bottom right and seamlessly recede when you don't need them. We've also reimagined the FaceTime landing page. It's now a space that celebrates your closest relationships with beautiful personalized contact posters.](#02:42)\n[- We have some great updates this year to make CarPlay more beautiful and even easier to use, including icons that look great in light or dark mode. We've also added the compact design when you get a call, so you can still see what's on the screen, like your upcoming directions, along with tap backs and pinned conversations in messages.](#03:15)\n[iOS 26 also gives you widgets in CarPlay. They're quick and glanceable ways to get information as well as live activities so you can stay on top of what's important to you, like a friend's flight status. For developers, the same widgets and live activities that you've developed for iPhone, work in CarPlay. All of these updates also come to CarPlay Ultra. CarPlay Ultra lets you choose the layout and design of the most important information for you and adds vehicle controls for features like the radio and climate right in the CarPlay experience.](#03:42)\n[- The phone app is fundamental to the iPhone experience. This year we're giving you the option to choose a new unified layout that brings together your favorites, recents and voicemails. Your favorites stay front and center, so you can start a call with just a tap. Your recents and voicemails now appear in a single convenient list below so you can easily scroll to see who reached out and what they said. And with Apple Intelligence, new voicemail summaries help surface the most important information.](#04:20)\n[Now when you receive a call from an unknown number, call screening helps you quickly gauge whether it's something important or just another pesky telemarketer. Call screening automatically answers calls from unknown numbers silently in the background. Once the caller shares their name and the reason for their call, your phone rings, and you can view their response and pick up, or ignore.](#04:48)\n[Next time you're stuck on hold, whether it's because you're changing your flight or dealing with utility bills, the phone app automatically detects hold music and asks if you want it to wait for you. When you tap hold, the music stops, but the call stays connected, so you can keep using your iPhone or even put it away and go about your daily routines. Once a live agent becomes available, we'll ring you to return to the call and let the agent know that you'll be there shortly.](#05:16)\n[Another way we stay connected is with messages. This year we're giving you an exciting new way to bring personality to those shared spaces with backgrounds. When you select a background, it appears for everyone in the conversation to enjoy. Group chats are home to so many age old questions. We're introducing polls. You can create a poll for anything, and with Apple Intelligence messages can detect when a poll might come in handy and suggest one, like if someone asks \"Where should we stay for our annual trips?\" Anyone in the group can contribute new ideas, then watch as the votes roll in live.](#05:40)\n[Now you can request, send, and receive Apple Cash to settle up on concert tickets or the dinner bill right from the group chat. We're also bringing typing indicators for your group chats, which lets you know who is about to chime in. We're bringing screening tools to messages that help you eliminate distractions. We're introducing the ability for you to screen new senders so your messages go from this, a list cluttered with spam and unknown numbers, to this.](#06:12)\n[- You can mix together two emoji to create something new, like a sloth and a light bulb...](#06:40)\n\n[For that moment when you're the last one in the group chat to get the joke, you have the option to change expressions, like making your friend look shocked or making them laugh. You can also adjust things like hairstyle. You have new ChatGPT styles, like an oil painting style for your friend's contact poster.](#06:47)\n[We're introducing live translation, integrated into messages, FaceTime, and phone, and it's enabled by Apple-built models that run entirely on device. For instance, in messages, live translation can automatically translate text for you. When you're catching up on FaceTime, you can follow along with translated live captions while you continue to hear your grandmother's voice.](#07:04)\n[(Speaking in foreign language) When you're on a phone call, your words are translated as you talk, and the translation is spoken out loud for the call recipients.](#07:29)\n[- [Voice Over] Hi, are you available to cater a wedding on December 6th? (soft music)](#07:44)\n[(Speaking in foreign language) With Apple Music, we're adding lyrics translation to help you understand the meaning behind your favorite songs, and lyrics pronunciation so everyone can sing along to the music regardless of language.](#07:50)\n[(Upbeat music) We're also introducing auto mix, which uses intelligence to seamlessly mix from one song to the next like a DJ, time stretching and beat matching at the perfect moment.](#08:07)\n[(Upbeat music) With music pins, just pin your favorite artist album or playlist to the top of the library.](#08:19)\n[Let's talk about maps. Your iPhone will now learn your preferred routes and offer them in maps alongside other options. And with the Maps widget, you can check your commute before you leave so you can get an idea of how long it'll take.](#08:35)\n[We're introducing Visited places. You can choose to have your iPhone detect when you're somewhere like a restaurant or shop and then view your visited places in the maps library, or simply search and share with the tap.](#08:48)\n[Next, wallet. With driver's licenses and wallet, we've created the most private and secure way for you to present your identity. They're currently available in nine states plus Puerto Rico, and starting this fall you can create a digital ID with your US passport, while not a replacement for your physical passport.](#09:02)\n[Digital ID can be used for domestic travel at supported TSA checkpoints in apps and in person where age and identity verification are required. And while at the airport you'll appreciate refreshed boarding passes and wallet, which now include convenient access to indoor mapping that gets you around the airport quickly, find my to track your bags right from the boarding pass and shareable life flight status, so loved ones can keep track of when you are landing.](#09:18)\n[Next Apple Pay. In addition to using rewards and installments while shopping online with Apple Pay, you can now choose to redeem your points or pay with installments when you shop in person.](#09:48)\n[With Apple Intelligence Wallet can now identify and summarize order tracking details beyond your Apple Pay orders. It will find emails sent by either merchants or delivery carriers and conveniently pull together your order details, order progress notifications and more.](#09:58)\n[- Introducing the games app, where you'll see what's most relevant, like updates and can't miss events in games you're actively playing. And over on the library tab you can see all the games you've ever downloaded from the app store.](#10:13)\n[Head over to the play together tab to see what your friends are playing, compare scores and achievements and invite them to play.](#10:28)\n[- We're extending visual intelligence to your iPhone screen so you can search and take action across your apps using anything you're viewing. Say open a social media app, and I see this great jacket, I wanna know where I can get something like this.](#10:36)\n[Now at the top, I have screenshot controls like markup and share. At the bottom I have visual intelligence tools like this image search button in the bottom right, I could tap here to find similar images on Google or other apps I use frequently supposedly come across an event I'm interested in, and I wanna add it to my calendar. Visual intelligence makes this easier than ever. Notice the add to calendar suggestion at the bottom. With Apple Intelligence, it can extract the date, time, and location and pre-populate them. I'll tap trade event, easy as that.](#10:52)\n[- Apple Watch is indispensable and with watchOS 26 we continue to push this further. It has the gorgeous new design with liquid glass. It brings a more expressive experience in places like the Smarts stack, the numerals of the photos face, control center and in-app navigation and controls. watchOS introduces an exciting new fitness feature, Workout Buddy.](#11:27)\n[- Workout Buddy is pretty incredible. It simultaneously gathers all the data from your workout.](#11:47)\n[It rapidly analyzes vast amounts of your fitness history. Combined with Apple Intelligence on your iPhone, it provides you with just the right encouragement as soon as you start your run. Workout Buddy can kick off with a peptide.](#11:54)\n[Great job starting your run. This is your second run this week, you're crushing it. Closing that move ring for six straight days. Let's keep building on that with your run today. You'll buy some house vibes from Murra Masa. After you end your workout it recognizes your effort.](#12:08)\n[Fantastic run. You average the pace of nine minutes and seven seconds per mile across 6.3 miles. It's personal and private and will be available starting in English across the most popular workout types. Now features like custom workout and race route are just a tap away, so you can easily create a custom workout with work and recovery intervals.](#12:25)\n[For your music, you can keep it simple and let Apple Music choose the best playlist based on the workout you're doing and the music you love. You can select playlists or podcasts that are suggested based on what you've been listening to for that type of workout.](#12:47)\n[Watch West also enhances features like the Smart Stack. This year the Smart stack improves its prediction algorithm by fusing on-device data like points of interest, location and more so it can accurately predict features that could be immediately useful to you.](#13:02)\n[So when you walk into your gym for that regular morning session, a smart stack hint will subtly appear and when you open the Smarts Stack, a widget will be ready for you to start your workout. Or when Apple Watch detects that you're in a remote location with no connectivity, a hint will appear to start backtrack to support you while you're off the grid.](#13:16)\n[We're also making Apple Watch more proactive with enhancements to notifications. You might use silent mode to avoid receiving loud alert at an inopportune moment, like that. But now Apple Watch can interpret the ambient noise in your environment and automatically adjust the volume of incoming calls and notifications, much better.](#13:35)\n[And when you receive a notification that you wanna address later, a new wrist flick gesture will dismiss it and take you back to your watch face. You can also use wrist flick to mute incoming calls, silence timers and alarms or even close the Smarts Stack.](#13:56)\n[The Messages app gets even better with Apple Intelligence Live translation, makes staying connected to your loved ones who speak a different language as simple as raising your wrist. Conversation backgrounds, elevate the experience, and now based on the context of the message, you can quickly perform new actions that are intelligently provided using machine learning, like sharing your location through find my, when someone's looking for you.](#14:10)\n[We're also giving you a quick new way to capture what's important with notes, which now comes to Apple Watch. It's perfect for when you wanna save a quick note to yourself or view an existing note.](#14:33)\n[watchOS also has new APIs for developers. The smart stack can intelligently show a widget for slopes when you arrive at a ski resort with improved location APIs and now dark noise can add a custom control to control center so it can easily turn on a sleep soundscape without picking up your iPhone.](#14:44)\n[With tvOS 26, we're making Apple TV more enjoyable than ever. App icons feel more vibrant thanks to their layer design and new specular highlights that bring depth and detail to every edge. You'll immediately notice how playback controls refract the content underneath, beautifully complimenting the action without distracting from the story.](#15:02)\n[This unobtrusive design carries through the control center and as you browse you'll see a bold new look with beautiful cinematic poster art. Now you can set Apple TV to display profiles whenever it wakes from sleep. And for developers, tvOS also introduces a new API for automatic sign in linking app logins to an Apple account. That means no more signing in across Each of your Apple devices.](#15:24)\n[Apple Music is another favorite. with tvOS singing along with Apple Music gets even better. Your iPhone becomes the mic, amplifying your voice through the TV with visual effects that light up the big screen. Everyone can join in using their own iPhone and add songs to the queue, react with onscreen emoji, or take a turn and sing along with their favorite artists.](#15:51)\n[Let me introduce you to macOS Tahoe. Widgets, the dock, and app icons have been refined with liquid glass sidebars and toolbars reflect the depth of your workspace and offer a subtle hint of the content with an easy reach as you scroll. The menu bar is now completely transparent, making your display feel larger and there are more ways to customize what controls appear in the menu bar.](#16:22)\n\n[And control center and how they're laid out. You can change the color of your folders and add a symbol or emoji to give them a unique identity.](#16:50)\n[Let's talk about the power of continuity. We are bringing live activities to Mac. So if you've ordered Uber Eats on your iPhone, the live activity also appears in the menu bar. And when you click, the app opens in iPhone mirroring so you can take action directly on your Mac.](#16:59)\n[We're also enhancing the calling experience by bringing the phone app to Mac. You can conveniently access your familiar content like recents, contacts and voicemail, synced from iPhone and easily make a call with just a click.](#17:18)\n[Shortcuts help people get more done faster every day. In macOS Tahoe you can run them automatically, for example, at a specific time of day or when you take actions like saving a file to a folder, or connecting to a display. And with Apple Intelligence we're introducing intelligent actions. For example, if you're a student, you can build a shortcut to compare your recording from a lecture to the notes you took and add any key points you missed. You can access Apple Intelligence models on device with private cloud compute or you can use ChatGPT.](#17:33)\n[Spotlight is getting its biggest update ever. It puts your most relevant files within Easy Reach, including intelligent suggestions based on your routines or what you're currently working on. We're also introducing system and app actions and Spotlight. You can take hundreds of actions from creating an event to starting an audio recording, or even playing a podcast. You can even fill out parameters for an action. Say you wanna send an email, you can write the message, set the recipient and add a subject all without lifting your hands off the keyboard. Plus mini bar items for the app you're in can be accessed right from Spotlight.](#18:07)\n[We're introducing Quick Keys. For instance, sending a message can be done by simply typing sm, and adding a new reminder can be done by typing ar, and then just typing the reminder. I have an image I copied to my clipboard. Lemme paste that in. Great. Next quick resize, I'll search for background removal and there's the pages menu item. I'll just hit return. There we go.](#18:45)\n[I can now use Spotlight to get to my clipboard history. Here it is on the right. This is super helpful to get back to an image, some text, or a link I copied earlier in the day. Spotlight knows I add docs to this ideas board regularly, so it actually offers that as an action right here. It's all ready to go with the document queued up in everything.](#19:13)\n[- It also levels up the gaming experience, starting with the new games app. A click on a controller brings up the new game overlay where you can adjust the system settings that matter most mid game. MacOS Tahoe introduces Metal 4, which brings next generation rendering technologies to games like frame interpolation, denoising and more.](#19:37)\n[- Let's start with some of the amazing new spatial experiences coming to visionOS, like Widgets. visionOS now has beautiful new widgets like clock with distinctive and exquisitely detailed clock face designs. Weather, that adapts to today's forecast. Music for quick access to your favorite tunes. All these and more can be found in the new widgets app.](#20:00)\n[iOS introduced us to spatial scenes and they're also coming to visionOS. - It uses a new AI algorithm that leverages computational depth to create multiple perspectives from your 2D photos making the images more lifelike. Spatial scenes will make your web browsing experience more engaging too. Simply select spatial browsing to transform supported articles, hiding distractions and revealing inline photos that come alive as you scroll.](#20:25)\n[We first release personas as a beta feature that represents you when you're on a video call. With visionOS 26 personas will undergo a dramatic transformation and more realistically represent you. Now you and your friends can watch the latest movie or play a spatial game together and this will be great for enterprise where users can collaborate using apps like 3D live from Dasso system to visualize 3D designs in person and with remote colleagues.](#20:52)\n[- With visionOS 26 organizations can easily share a common pool of devices among team members and you can securely save your eye and hand data, vision prescription and accessibility settings to your iPhone so you can quickly start to use a shared team device or a friend's Vision Pro as a guest user, the new protected content API, it's literally a for your eyes only mode, ensuring that only people who've been granted access can see confidential materials like medical records or business forecasts.](#21:19)\n[We're excited to share Logitech Muse, built for Vision Pro, which unlocks entirely new creative workflows. Logitech Muse will let you draw and collaborate in three dimensions with precision in apps like Spatial Analog.](#21:53)\n[Sony is bringing support for PlayStation VR 2 sense controller to visionOS 26. And with visionOS 26, we're enabling more ways to produce great new content, like the brand new Adobe app for visionOS, powered by Premier, that will let you edit and preview spatial video directly in Vision Pro. We've partnered with GoPro, Insta360, and Canon to support native playback of 180, 360, and wide field of view video on visionOS.](#22:08)\n[iPadOS 26 is a giant release. This is what you've all been waiting for: a new windowing system on iPad. When I first open an app, it shows up full screen. I can swipe home and open another, and it's still full screen. So if this is how you like to use iPad, it's just as simple as ever. But now I also have this grab handle in the bottom right to fluidly resize apps into Windows. If I previously resized apps, when I open them again, they open back in the exact same size and position, and I can place them just where I want them. This is designed to work great with touch or with a trackpad.](#22:40)\n[And now I also have a pointer that's more precise and responsive. When I use it to hover over these new buttons in the top left, they expand into familiar controls. I can use these to close or minimize my windows. Another way I can arrange my windows is with tiling. Since this is designed for the unique qualities of iPad, I can simply flick windows towards the edges to tile. There's even a little grabber here to resize both at the same time. If I click and hold on my window controls, I'll see even more tiling options. Let's choose quarters. I can simply swipe home to peek at my home screen. Tapping an app opens it along with all my previous windows.](#23:17)\n[To quickly see all my open windows, I now have expose, with a swipe up from the bottom and hold, my windows spread out, and a tap brings the one I need to the front. So we're also adding a menu bar on iPad. It's always available from the top of the screen. It's arranged to be familiar with clear labeled options, and it's a great way to find the features you need quickly.](#24:03)\n[The new Windowing architecture has enabled us to bring it to every iPad that runs iPadOS 26, including iPad and iPad Mini.](#24:29)\n[We're super charging the Files app. It now includes features like an updated list view to see more details in resizable columns and collapsible folders. It also gets the same folder customization options as macOS with custom colors and icons that sync across devices, making it easy to spot the one you're looking for. And you can now choose which app you wanna open a file with and set it as a default, like picking Photoshop, VSCO, or Darkroom for opening images. For easier access to your files, you can now put folders in the dock. Simply drag any folder from the Files app right into the dock. When you tap on one, it fans out showing the files inside.](#24:38)\n[And to help you with that, we're bringing a beloved app from MacOS to iPadOS: Preview. And it also includes familiar tools to edit images and export them in a variety of different formats and sizes.](#25:19)\n[We're also enabling more complex workflows for working with audio and video. To help easily specify which microphone to use, we added an audio input selector. We're making voice isolation available no matter what app is being used, keeping voices clear and crisp. Local capture lets you produce amazing content with high quality recordings right from iPad. It works with any of your favorite video conferencing apps. Just turn it on in control center and your own local high quality audio and video are captured on your iPad. Echo cancellation of other participants audio keeps your voice front and center, so you get a clear recording of your side of the call. Once it's over, everyone who use local capture can easily share their own audio and video files.](#25:32)\n[When it's time to edit and export your content, we're making that even easier by enabling background tasks on iPad. If you start an extended process like an export and switch to another app, the task will continue to run. Background tasks show up as live activities, so you always know what's running, and you always have full control.](#26:19)\n[And there's even more that we didn't have time to cover today, like advanced 3D graphing capabilities in math notes, a new read pen for a traditional calligraphy experience, and the journal app, which comes to iPad with Apple pencil support. Icon Composer helps developers and designers create layered icons that look stunning in light, dark, tinted, and all clear look, visualizing how light interacts with liquid glass. And we're bringing more generative intelligence to Xcode. Predictive code completion gets more accurate using larger context from your code. And we've expanded our vision for swift assist where developers interact with code.](#26:39)\n[Using natural language directly in Xcode. Our new OS releases will be available as developer betas today, with a public beta coming next month, and they will be available to all users this fall.](#27:22)\n[And thanks to everyone for joining us today. We've got a big week ahead of us. Let's go have a great WWDC.](#27:37)",
          format: 'subtitle-formatted',
          raw: 'raw-transcript',
          language: 'en-US',
          status: 'completed',
        },
        {
          id: 'content-raw',
          block_id: 'block-1',
          raw: 'raw-transcript',
          plain:
            "0:00: - Good morning, and welcome to WWDC.\n0:04: Here's Craig.\n0:05: - First is Apple Intelligence.\n0:07: We're opening up access for any app\n0:10: to tap directly into the on-device large language model\n0:14: at the core of Apple Intelligence\n0:16: with a new Foundation Models framework.\n0:19: This gives developers direct access\n0:21: to intelligence that's powerful,\n0:23: fast, built with privacy,\n0:25: and available even when you're offline.\n0:28: For example, if you're getting ready for an exam,\n0:30: an app like Kahoot! can create a personalized quiz\n0:33: from your notes to make studying more engaging.\n0:36: And because it uses on-device models,\n0:38: this happens without cloud API costs.\n0:41: - And for the first time,\n0:42: we're introducing a universal design across our platforms,\n0:46: and it starts with an entirely new expressive material\n0:50: we call liquid glass.\n0:52: It transforms depending on your content or even your context\n0:56: and brings more clarity to navigation and controls.\n0:60: It beautifully refracts light\n1:01: and dynamically reacts to your movement\n1:04: with specular highlights.\n1:05: Elements once considered\n1:06: for rectangular displays have been redesigned\n1:09: to fit perfectly concentric\n1:10: with the rounded corners of the hardware.\n1:13: - The lock screen has been updated\n1:14: with the time and controls now made of liquid glass\n1:18: with this beautiful glass edge\n1:20: and a new sense of responsiveness.\n1:23: Whether you use dark mode\n1:25: or one of our entirely new styles,\n1:28: like our all clear look.\n1:30: I'll switch to my photo shuffle wallpaper.\n1:32: And watch as I switch images.\n1:35: The time now fluidly adapts to the available space.\n1:39: Its San Francisco typeface has been uniquely crafted\n1:43: to dynamically scale the weight width\n1:46: and height of each numeral\n1:48: in order to nestle into the scene.\n1:50: It also keeps the best part of my photo\n1:53: and view as new information comes in,\n1:55: like a message from a friend or an email from a colleague.\n1:59: With advanced computer vision techniques running\n2:01: on the neural engine, we generate a spatial scene\n2:04: from your 2D photos creating a delightful 3D effect,\n2:08: bringing your favorite memories to life\n2:11: as you move your iPhone in your hand.\n2:13: Next, let's talk camera.\n2:16: A simplified and streamlined design\n2:18: elevates the two capture modes that you use most.\n2:21: Photo and video, just swipe your finger left\n2:24: or right to reveal additional modes like cinematic mode,\n2:28: portrait mode, and a simple swipe up lets you easily reach\n2:32: all your settings like aspect ratio and timers.\n2:35: Photos now features separate tabs for library\n2:39: and for collections, where you'll find your favorites,\n2:42: your albums, and easy access to search webpages\n2:47: now flow edge to edge to the very bottom of the screen,\n2:50: enabling you to see more of the page.\n2:53: The tab bar has been redesigned to float\n2:55: above the webpage and surfaces.\n2:57: Frequently used actions like search and refresh.\n3:00: When on a call, your most important controls\n3:03: now float on the bottom right and seamlessly recede\n3:06: when you don't need them.\n3:07: We've also reimagined the FaceTime landing page.\n3:11: It's now a space that celebrates your closest relationships\n3:15: with beautiful personalized contact posters.\n3:18: - We have some great updates this year\n3:19: to make CarPlay more beautiful and even easier to use,\n3:24: including icons that look great in light or dark mode.\n3:28: We've also added the compact design when you get a call,\n3:32: so you can still see what's on the screen,\n3:35: like your upcoming directions.\n3:36: Along with tap backs and pinned conversations in messages.\n3:43: iOS 26 also gives you widgets in CarPlay.\n3:47: They're quick and glanceable ways to get information\n3:50: as well as live activities so you can stay on top\n3:53: of what's important to you, like a friend's flight status.\n3:56: For developers, the same widgets and live activities\n3:59: that you've developed for iPhone, work in CarPlay.\n4:03: All of these updates also come to CarPlay Ultra.\n4:07: CarPlay Ultra lets you choose the layout and design\n4:11: of the most important information for you\n4:14: and adds vehicle controls for features like the radio\n4:18: and climate right in the CarPlay experience.\n4:22: - The phone app is fundamental to the iPhone experience.\n4:26: This year we're giving you the option\n4:28: to choose a new unified layout\n4:29: that brings together your favorites, recents and voicemails.\n4:32: Your favorites stay front and center.\n4:34: So you can start a call with just a tap.\n4:37: Your recents and voicemails now appear in a single\n4:39: convenient list below so you can easily scroll\n4:42: to see who reached out and what they said.\n4:44: And with Apple Intelligence,\n4:46: new voicemail summaries help service\n4:48: the most important information.\n4:50: Now when you receive a call from an unknown number,\n4:53: call screening helps you quickly gauge\n4:55: whether it's something important\n4:56: or just another pesky telemarketer.\n4:58: Call screening automatically answers calls\n5:01: from unknown numbers silently in the background.\n5:04: Once the caller shares their name\n5:06: and the reason for their call, your phone rings.\n5:08: (phone ringing)\n5:10: And you can view their response and pick up, or ignore.\n5:17: Next time you're stuck on hold,\n5:18: whether it's because you're changing your flight\n5:20: or dealing with utility bills.\n5:21: The phone app automatically detects hold music\n5:24: and asks if you want it to wait for you.\n5:26: When you tap hold, the music stops,\n5:28: but the call stays connected,\n5:30: so you can keep using your iPhone or even put it away\n5:33: and go about your daily routines.\n5:35: Once a live agent becomes available,\n5:36: we'll ring you to return to the call\n5:38: and let the agent know that you'll be there shortly.\n5:41: Another way we stay connected is with messages.\n5:43: This year we're giving you an exciting new way\n5:45: to bring personality\n5:47: to those shared spaces with backgrounds.\n5:49: When you select a background,\n5:50: it appears for everyone in the conversation to enjoy.\n5:54: Group chats are home to so many age old questions.\n5:56: We're introducing polls.\n5:58: You can create a poll for anything,\n6:00: and with Apple Intelligence messages can detect\n6:03: when a poll might come in handy and suggest one,\n6:06: like if someone asks Where should we stay\n6:08: for our annual trips?\n6:09: Anyone in the group can contribute new ideas,\n6:12: then watch as the votes roll in live.\n6:15: Now you can request send and receive Apple Cash\n6:18: to settle up on concert tickets\n6:20: or the dinner bill right from the group chat.\n6:23: We're also bringing typing indicators for your group chats,\n6:26: which lets you know who is about to chime in.\n6:29: We're bringing screening tools to messages\n6:30: that help you eliminate distractions.\n6:32: We're introducing the ability for you to screen new senders\n6:35: so your messages go from this,\n6:38: a list cluttered with spam and unknown numbers. to this.\n6:41: - You can mix together two emoji\n6:43: to create something new, like a sloth and a light bulb\n6:47: for that moment when you're the last one in the group chat\n6:49: to get the joke, you have the option to change expressions\n6:53: like making your friend look shocked or making them laugh.\n6:57: You can also adjust things like hairstyle.\n6:60: You have new ChatGPT styles like an oil painting style\n7:03: for your friend's contact poster.\n7:05: We're introducing live translation.\n7:08: It's integrated into messages, FaceTime and phone,\n7:11: and it's enabled by Apple built models\n7:14: that run entirely on device.\n7:16: For instance, in messages,\n7:17: live translation can automatically translate text for you.\n7:21: When you're catching up on FaceTime,\n7:23: you can follow along with translated live captions\n7:26: while you continue to hear your grandmother's voice.\n7:29: (speaking in foreign language)\n7:36: When you're on a phone call,\n7:37: your words are translated as you talk\n7:40: and the translation is spoken out loud\n7:42: for the call recipients.\n7:44: - [Voice Over] Hi, are you available to cater a wedding\n7:47: on December 6th? (soft music)\n7:50: (speaking in foreign language)\n7:53: - With Apple Music, we're adding lyrics translation\n7:56: to help you understand the meaning\n7:58: behind your favorite songs.\n8:00: And lyrics pronunciation so everyone can sing along\n8:04: to the music regardless of language.\n8:07: (upbeat music)\n8:08: We're also introducing auto mix,\n8:11: which uses intelligence to seamlessly mix\n8:13: from one song to the next like a DJ,\n8:16: time stretching and beat matching at the perfect moment.\n8:19: (upbeat music)\n8:29: With music pins, just pin your favorite artist album\n8:32: or playlist to the top of the library.\n8:35: Let's talk about maps.\n8:37: Your iPhone will now learn your preferred routes\n8:39: and offer them in maps alongside other options.\n8:42: And with the Maps widget, you can check your commute\n8:45: before you leave so you can get an idea\n8:47: of how long it'll take.\n8:48: We're introducing Visited places.\n8:51: You can choose to have your iPhone detect\n8:53: when you're somewhere like a restaurant or shop\n8:55: and then view your visited places in the maps library.\n8:59: Or simply search and share with the tap.\n9:02: Next, wallet.\n9:03: With driver's, licenses and wallet,\n9:05: we've created the most private and secure way\n9:08: for you to present your identity.\n9:10: They're currently available in nine states plus Puerto Rico\n9:13: and starting this fall you can create a digital ID\n9:16: with your US passport,\n9:18: while not a replacement for your physical passport.\n9:21: Digital ID can be used for domestic travel\n9:23: at supported TSA checkpoints in apps and in person\n9:27: where age and identity verification are required.\n9:30: And while at the airport you'll appreciate refreshed\n9:33: boarding passes and wallet, which now include convenient\n9:36: access to indoor mapping\n9:37: that gets you around the airport quickly.\n9:40: Find my to track your bags right from the boarding pass\n9:43: and shareable life flight status.\n9:45: So loved ones can keep track of when you are landing.\n9:48: Next Apple Pay.\n9:50: In addition to using rewards and installments\n9:52: while shopping online with Apple Pay,\n9:54: you can now choose to redeem your points or pay\n9:57: with installments when you shop in person.\n9:59: With Apple Intelligence Wallet can now identify\n10:02: and summarize order tracking details\n10:04: beyond your Apple Pay orders.\n10:06: It will find emails sent by either merchants\n10:08: or delivery carriers and conveniently pull together\n10:10: your order details, order progress notifications and more.\n10:14: - Introducing the games app,\n10:17: where you'll see what's most relevant,\n10:19: like updates and can't miss events in games\n10:22: you're actively playing.\n10:23: And over on the library tab\n10:25: you can see all the games\n10:27: you've ever downloaded from the app store.\n10:29: Head over to the play together tab\n10:31: to see what your friends are playing,\n10:33: compare scores and achievements and invite them to play.\n10:37: - We're extending visual intelligence\n10:39: to your iPhone screen so you can search\n10:42: and take action across your apps\n10:44: using anything you're viewing.\n10:46: Say open a social media app, and I see this great jacket,\n10:50: I wanna know where I can get something like this.\n10:53: Now at the top, I have screenshot\n10:56: controls like markup and share.\n10:58: At the bottom I have visual intelligence tools\n11:00: like this image search button in the bottom right,\n11:03: I could tap here to find similar images on Google\n11:07: or other apps I use frequently supposedly come across\n11:09: an event I'm interested in,\n11:11: and I wanna add it to my calendar.\n11:12: Visual intelligence makes this easier than ever.\n11:15: Notice the add to calendar suggestion at the bottom.\n11:18: With Apple Intelligence, it can extract the date, time,\n11:21: and location and pre-populate them.\n11:24: I'll tap trade event, easy as that.\n11:28: - Apple Watch is indispensable and with watchOS 26\n11:31: we continue to push this further.\n11:33: It has the gorgeous new design with liquid glass.\n11:36: It brings a more expressive experience in places\n11:38: like the Smarts stack,\n11:39: the numerals of the photos face,\n11:41: control center and in-app navigation and controls.\n11:44: watchOS introduces an exciting new fitness feature.\n11:47: Workout Buddy.\n11:48: - Workout Buddy is pretty incredible.\n11:51: It simultaneously gathers all the data from your workout\n11:54: and rapidly analyzes vast amounts of your fitness history.\n11:59: Combined with Apple Intelligence on your iPhone,\n12:01: it provides you with just the right encouragement\n12:04: as soon as you start your run,\n12:06: Workout Buddy can kick off with a peptide.\n12:09: - [Voice Over 2] Great job starting your run.\n12:11: This is your second run this week, you're crushing it.\n12:14: Closing that move ring for six straight days.\n12:17: Let's keep building on that with your run today.\n12:19: You'll buy some house vibes from Murra Masa.\n12:22: - After you end your workout it recognizes your effort.\n12:26: - [Voice Over 2] Fantastic run.\n12:28: You average the pace of nine minutes\n12:29: and seven seconds per mile across 6.3 miles.\n12:33: - It's personal and private\n12:35: and will be available starting in English\n12:37: across the most popular workout types.\n12:39: Now features like custom workout\n12:41: and race route are just a tap away.\n12:44: So you can easily create a custom workout\n12:46: with work and recovery intervals.\n12:48: For your music, you can keep it simple and let Apple Music\n12:51: choose the best playlist based on the workout you're doing\n12:54: and the music you love.\n12:55: You can select playlists or podcasts\n12:58: that are suggested based\n12:59: on what you've been listening to for that type of workout.\n13:02: - Watch West also enhances features like the Smart Stack.\n13:06: This year the Smart stack improves its prediction algorithm\n13:09: by fusing on-device data like points of interest,\n13:12: location and more so it can accurately predict features\n13:15: that could be immediately useful to you.\n13:18: So when you walk into your gym\n13:19: for that regular morning session,\n13:21: a smart stack hint will subtly appear\n13:23: and when you open the Smarts Stack,\n13:25: a widget will be ready for you to start your workout.\n13:28: Or when Apple Watch detects that you're in a remote location\n13:31: with no connectivity, a hint will appear to start backtrack\n13:34: to support you while you're off the grid.\n13:36: We're also making Apple Watch more proactive\n13:38: with enhancements to notifications.\n13:40: You might use silent mode to avoid receiving loud alert\n13:43: at an inopportune moment, like that.\n13:47: But now Apple Watch can interpret the ambient noise\n13:50: in your environment and automatically adjust the volume\n13:53: of incoming calls and notifications, much better.\n13:57: And when you receive a notification\n13:59: that you wanna address later,\n14:00: a new wrist flick gesture will dismiss it\n14:03: and take you back to your watch face.\n14:05: You can also use wrist flick to mute incoming calls,\n14:07: silence timers and alarms\n14:09: or even close the Smarts Stack.\n14:11: the Messages app gets even better\n14:13: with Apple Intelligence Live translation.\n14:15: makes staying connected to your loved ones who speak\n14:17: a different language as simple as raising your wrist.\n14:20: conversation backgrounds, elevate the experience,\n14:24: and now based on the context of the message,\n14:26: you can quickly perform new actions that are intelligently\n14:29: provided using machine learning,\n14:31: like sharing your location through find my,\n14:32: When someone's looking for you.\n14:34: We're also giving you a quick new way\n14:35: to capture what's important with notes,\n14:38: which now comes to Apple Watch.\n14:40: It's perfect for when you wanna save a quick note\n14:42: to yourself or view an existing note.\n14:44: watchOS also has new APIs for developers.\n14:48: The smart stack can intelligently show a widget\n14:50: for slopes when you arrive at a ski resort\n14:52: with improved location APIs\n14:55: and now dark noise can add a custom control\n14:57: to control center so it can easily\n14:59: turn on a sleep soundscape without picking up your iPhone.\n15:03: - With tvOS 26,\n15:04: we're making Apple TV more enjoyable than ever.\n15:08: App icons feel more vibrant thanks to their layer design\n15:12: and new specular highlights that bring depth\n15:14: and detail to every edge.\n15:16: You'll immediately notice how playback controls\n15:18: refract the content underneath,\n15:20: beautifully complimenting the action\n15:22: without distracting from the story.\n15:25: This unobtrusive design carries through the control center\n15:28: and as you browse you'll see a bold new look\n15:31: with beautiful cinematic poster art.\n15:34: Now you can set Apple TV to display profiles\n15:37: whenever it wakes from sleep.\n15:39: And for developers, tvOS also introduces a new API\n15:43: for automatic sign in linking app logins\n15:46: to an Apple account.\n15:48: That means no more signing in across\n15:50: Each of your Apple devices.\n15:52: Apple Music is another favorite.\n15:55: with tvOS singing along with Apple Music gets even better.\n16:00: Your iPhone becomes the mic,\n16:02: amplifying your voice through the TV with visual effects\n16:06: that light up the big screen.\n16:07: Everyone can join in using their own iPhone and add songs\n16:11: to the queue, react with onscreen emoji,\n16:14: or take a turn and sing along with their favorite artists.\n16:18: (upbeat music)\n16:23: - Let me introduce you to macOS Tahoe.\n16:27: Widgets, the dock, and app icons have been refined\n16:32: with liquid glass sidebars and toolbars reflect the depth\n16:36: of your workspace and offer a subtle hint of the content\n16:39: with an easy reach as you scroll.\n16:42: The menu bar is now completely transparent,\n16:44: making your display feel larger and there are more ways\n16:48: to customize what controls appear in the menu bar\n16:50: and control center and how they're laid out.\n16:53: You can change the color of your folders\n16:55: and add a symbol or emoji to give them a unique identity.\n16:59: Let's talk about the power of continuity.\n17:02: We are bringing live activities to Mac.\n17:05: So if you've ordered Uber Eats on your iPhone,\n17:08: the live activity also appears in the menu bar.\n17:11: And when you click, the app opens in iPhone mirroring\n17:15: so you can take action directly on your Mac.\n17:18: We're also enhancing the calling experience\n17:21: by bringing the phone app to Mac.\n17:23: You can conveniently access your familiar content\n17:26: like recents, contacts and voicemail,\n17:28: synced from iPhone and easily make a call with just a click.\n17:33: Shortcuts help people get more done faster every day.\n17:36: In macOS Tahoe you can run them automatically,\n17:39: for example, at a specific time of day\n17:42: or when you take actions like saving a file to a folder,\n17:45: or connecting to a display.\n17:47: And with Apple Intelligence\n17:49: we're introducing intelligent actions.\n17:51: For example, if you're a student,\n17:53: you can build a shortcut to compare your recording\n17:56: from a lecture to the notes you took\n17:58: and add any key points you missed.\n18:00: You can access Apple Intelligence models\n18:03: on device with private cloud compute or you can use ChatGPT.\n18:07: Spotlight is getting its biggest update ever.\n18:10: It puts your most relevant files within Easy Reach,\n18:13: including intelligent suggestions based on your routines\n18:16: or what you're currently working on.\n18:18: We're also introducing system and app actions and Spotlight.\n18:22: You can take hundreds of actions from creating an event\n18:26: to starting an audio recording, or even playing a podcast.\n18:29: You can even fill out parameters for an action.\n18:32: Say you wanna send an email, you can write the message,\n18:35: set the recipient and add a subject\n18:38: all without lifting your hands off the keyboard.\n18:40: Plus mini bar items for the app you're in can be accessed\n18:44: right from Spotlight.\n18:45: We're introducing Quick Keys.\n18:47: For instance, sending a message\n18:49: can be done by simply typing sm\n18:52: and adding a new reminder can be done by typing ar,\n18:56: and then just typing the reminder,\n18:58: I have an image I copied to my clipboard.\n19:01: Lemme paste that in. Great.\n19:03: Next quick resize, I'll search for background removal\n19:07: and there's the pages menu item.\n19:10: I'll just hit return. There we go.\n19:13: I can now use Spotlight to get to my clipboard history.\n19:17: Here it is on the right.\n19:20: This is super helpful to get back to an image,\n19:23: some text, or a link I copied earlier in the day.\n19:27: Spotlight knows I add docs to this ideas board regularly,\n19:31: so it actually offers that as an action right here.\n19:34: It's all ready to go\n19:36: with the document queued up in everything.\n19:38: - It also levels up the gaming experience,\n19:41: starting with the new games app.\n19:43: A click on a controller brings up the new game overlay\n19:47: where you can adjust the system settings\n19:49: that matter most mid game.\n19:51: MacOS Tahoe introduces Metal 4,\n19:54: which brings next generation rendering technologies\n19:57: to games like frame interpolation, denoising and more.\n20:01: - Let's start with some of the amazing\n20:03: new spatial experiences coming to visionOS, like Widgets.\n20:07: visionOS now has beautiful new widgets\n20:09: like clock with distinctive\n20:11: and exquisitely detailed clock face designs.\n20:15: Weather, that adapts to today's forecast.\n20:19: Music for quick access to your favorite tunes.\n20:22: All these and more can be found in the new widgets app.\n20:26: iOS introduced us to spatial scenes\n20:28: and they're also coming to visionOS.\n20:31: - It uses a new AI algorithm that leverages\n20:33: computational depth to create multiple perspectives\n20:36: from your 2D photos making the images more lifelike.\n20:40: Spatial scenes will make your web browsing\n20:41: experience more engaging too.\n20:43: Simply select spatial browsing\n20:45: to transform supported articles,\n20:47: hiding distractions and revealing inline photos\n20:50: that come alive as you scroll.\n20:52: We first release personas as a beta feature\n20:54: that represents you when you're on a video call.\n20:57: With visionOS 26 personas will undergo a dramatic\n20:60: transformation and more realistically represent you.\n21:05: Now you and your friends can watch the latest movie\n21:08: or play a spatial game together\n21:10: and this will be great for enterprise\n21:12: where users can collaborate using apps like 3D live\n21:15: from Dasso system to visualize 3D designs\n21:18: in person and with remote colleagues.\n21:21: - With visionOS 26 organizations can easily share\n21:25: a common pool of devices among team members\n21:28: and you can securely save your eye and hand data,\n21:31: vision prescription and accessibility settings\n21:34: to your iPhone so you can quickly start\n21:36: to use a shared team device\n21:38: or a friend's Vision Pro as a guest user,\n21:40: the new protected content API,\n21:43: it's literally a for your eyes only mode,\n21:46: ensuring that only people who've been granted access\n21:49: can see confidential materials like medical records\n21:52: or business forecasts.\n21:53: We're excited to share Logitech Muse, built for Vision Pro,\n21:58: which unlocks entirely new creative workflows.\n22:01: Logitech Muse will let you draw and collaborate\n22:04: in three dimensions with precision\n22:06: in apps like Spatial Analog.\n22:08: Sony is bringing support\n22:10: for PlayStation VR 2 sense controller to visionOS 26.\n22:16: And with visionOS 26, we're enabling more ways\n22:19: to produce great new content.\n22:21: Like the brand new Adobe app for visionOS,\n22:24: powered by Premier that will let you edit\n22:27: and preview spatial video directly in Vision Pro.\n22:31: We've partnered with GoPro, Insta360, and Canon to support\n22:36: native playback of 180, 360 and wide field of view video\n22:40: on visionOS.\n22:41: iPadOS 26 is a giant release.\n22:46: This is what you've all been waiting for.\n22:48: A new windowing system on iPad.\n22:52: - When I first open an app, it shows up full screen.\n22:55: I can swipe home and open another\n22:58: and it's still full screen.\n22:60: So if this is how you like to use iPad,\n23:02: it's just as simple as ever.\n23:04: But now I also have this grab handle in the bottom right\n23:07: to fluidly resize apps into Windows.\n23:10: If I previously resized apps, when I open them again,\n23:13: they open back in the exact same size and position\n23:17: and I can place them just where I want them.\n23:20: This is designed to work great\n23:21: with touch or with a track pad.\n23:24: And now I also have a pointer\n23:26: that's more precise and responsive.\n23:28: When I use it to hover over these new buttons\n23:30: in the top left, they expand into familiar controls.\n23:34: I can use these to close or minimize my windows.\n23:38: Another way I can arrange my windows is with tiling.\n23:41: Since this is designed for the unique qualities of iPad,\n23:44: I can simply flick windows towards the edges to tile.\n23:48: There's even a little grabber here\n23:51: to resize both at the same time.\n23:52: If I click and hold on my window controls,\n23:55: I'll see even more tiling options.\n23:58: Let's choose quarters.\n23:60: I can simply swipe home to peek at my home screen.\n24:03: Tapping an app opens it along with all my previous windows\n24:06: to quickly see all my open windows,\n24:08: I now have expose, with a swipe up from the bottom\n24:11: and hold my windows spread out\n24:13: and a tap brings the one I need to the front.\n24:15: So we're also adding a menu bar on iPad.\n24:19: It's always available from the top of the screen.\n24:21: It's arranged to be familiar with clear labeled options\n24:26: and it's a great way to find the features you need quickly.\n24:30: - The new Windowing architecture has enabled us\n24:33: to bring it to every iPad that runs iPadOS 26,\n24:36: including iPad and iPad Mini.\n24:39: - We're super charging the Files app.\n24:41: It now includes features like an updated list view\n24:44: to see more details in resizable columns\n24:47: and collapsible folders.\n24:49: It also gets the same folder customization options as macOS\n24:53: with custom colors and icons that sync across devices,\n24:57: making it easy to spot the one you're looking for.\n24:59: And you can now choose which app you wanna open a file with,\n25:03: and set it as a default, like picking Photoshop, VSCO,\n25:06: or Darkroom for opening images.\n25:08: For easier access to your files,\n25:10: you can now put folders in the dock.\n25:12: Simply drag any folder from the Files app\n25:15: right into the dock.\n25:16: When you tap on one, it fans out showing the files inside.\n25:20: And to help you with that, we're bringing a beloved app\n25:23: from MacOS to iPadOS, preview.\n25:26: And it also includes familiar tools to edit images\n25:29: and export them in a variety of different formats and sizes.\n25:33: We're also enabling more complex workflows\n25:36: for working with audio and video.\n25:38: To help easily specify which microphone to use,\n25:41: we added an audio input selector.\n25:43: We're making voice isolation available\n25:45: no matter what app is being used.\n25:46: Keeping voices clear and crisp.\n25:49: Local capture lets you produce amazing content\n25:52: with high quality recordings right from iPad.\n25:55: It works with any of your favorite video conferencing apps.\n25:59: Just turn it on in control center and your own local\n26:02: high quality audio and video are captured on your iPad,\n26:06: echo cancellation of other participants audio\n26:08: keeps your voice front and center\n26:10: so you get a clear recording of your side of the call.\n26:14: Once it's over, everyone who use local capture\n26:17: can easily share their own audio and video files.\n26:20: When it's time to edit and export your content,\n26:23: we're making that even easier\n26:24: by enabling background tasks on iPad.\n26:27: If you start an extended process like an export\n26:30: and switch to another app, the task will continue to run.\n26:34: Background tasks show up as live activities\n26:36: so you always know what's running\n26:38: and you always have full control.\n26:40: - And there's even more that we didn't have time\n26:43: to cover today, like advanced 3D graphing capabilities\n26:46: in math notes.\n26:48: A new read pen for a traditional calligraphy experience.\n26:51: And the journal app, which comes to iPad\n26:53: with Apple pencil support.\n26:55: Icon Composer helps developers and designers\n26:58: create layered icons that look stunning in light,\n27:02: dark, tinted, and all clear look visualizing\n27:05: how light interacts with liquid glass.\n27:09: And we're bringing more generative intelligence to Xcode.\n27:13: Predictive code completion gets more accurate\n27:15: using larger context from your code.\n27:18: And we've expanded our vision for swift assist\n27:21: where developers interact with code\n27:22: using natural language directly in Xcode.\n27:25: - Our new OS releases will be available\n27:28: as developer betas today,\n27:30: with a public beta coming next month\n27:33: and they will be available to all users this fall.\n27:37: And thanks to everyone for joining us today.\n27:40: We've got a big week ahead of us.\n27:42: Let's go have a great WWDC. (upbeat music)",
          format: 'subtitle',
          language: 'en',
          status: 'completed',
        },
      ],
      current_content_id: '0197ee94-a861-757c-afdb-22f9235bba77',
      type: 'transcript',
    },
    overview: {
      contents: [
        {
          id: 'content-processed',
          block_id: 'overview-block',
          raw: 'overview-content',
          plain:
            "Apple's WWDC 2025 keynote introduced significant advancements including Apple Intelligence, a universal design with liquid glass, enhanced camera features, and updates to iOS and watchOS. The event showcased innovations in messaging, music, maps, wallet functionalities, and new gaming experiences across devices.\n### Keynote Highlights\n#### Apple Intelligence\n* Introduction of the Foundation Models framework for app developers to access on-device large language models. [citation-00:07]\n* Example use case: Kahoot! app creating personalized quizzes for exam preparation without cloud costs. [citation-00:28]\n#### Universal Design\n* Launch of liquid glass material that adapts based on content and context for improved navigation clarity. [citation-00:41]\n* Updated lock screen design featuring dynamic elements that respond to user interaction. [citation-01:13]\n#### Enhanced Camera Features\n* Streamlined capture modes allowing quick access to photo and video settings. [citation-02:16]\n* Redesigned FaceTime landing page focusing on personalized contact posters. [citation-03:11]\n#### Messaging and Communication Updates* New features in Messages including polls, background customization, and live translation capabilities. [citation-05:43]\n* Call screening feature that provides information about unknown callers before connecting. [citation-04:50]\n#### Wallet and Payment Innovations\n* Introduction of digital ID capabilities for driver’s licenses in select states with secure verification methods at TSA checkpoints. [citation-09:02]\n* Enhanced order tracking through Apple Pay summarizing details from merchants automatically. [citation-10:06]",
          format: 'llm-output',
          language: 'en-US',
          status: 'completed',
        },
      ],
      current_content_id: 'content-processed',
      type: 'overview',
    },
  },
  id4YRO7G0wE: {
    id: 'id4YRO7G0wE',
    title: 'The AI Revolution Is Underhyped | Eric Schmidt | TED',
    author: [
      {
        name: 'TED',
        picture:
          'https://cdn.gooo.ai/web-images/4f224ab2e53d508d86735c5ba555ca26d2c50e7298ab94c4d24ebcbea4a3a6fa',
      },
    ],
    views: '2.1M',
    published_at: 'May 5, 2025',
    created_at: '2025-07-15T02:13:23.322Z',
    hero_image_url:
      'https://cdn.gooo.ai/web-images/126d476cce76227eb834d0a60101fa1a32f39383f94800934964cfa4ce3b17ef',
    play_url: 'https://www.youtube.com/watch?v=id4YRO7G0wE',
    transcript: {
      contents: [
        {
          id: '01980bdb-fadf-7529-b253-5e52d8f9bf77',
          block_id: '01980c2c-6f95-750d-b91b-1bbe044ccecb',
          plain:
            "[**Bilawal Sidhu (BS):** Let's go back. You said the arrival of non-human intelligence is a very big deal, and this photo, taken in 2016, feels like one of those quiet moments where the Earth shifted beneath us, but not everyone noticed. What did you see back then that the rest of us might have missed?](#00:08)\n[**Eric Schmidt (ES):** We understood that these algorithms were new and powerful. What happened in this particular set of games was, in roughly the second game, there was a new move invented by AI in a game that had been around for 2,500 years that no one had ever seen. Technically, the way this occurred was that the system of AlphaGo was essentially organized to always maintain a greater than 50 percent chance of winning. And so it calculated correctly this move, which was this great mystery among all of the Go players who are obviously insanely brilliant, mathematical, and intuitive players.](#00:26)\n[The question that Henry, Craig Mundie, and I started to discuss is, what does this mean? How is it that our computers could come up with something that humans had never thought about? I mean, this is a game played by billions of people, and that began the process that led to two books. And I think, frankly, is the point at which the revolution really started.](#01:06)\n[**BS:** It seems that all anyone can talk about is AI, especially here at TED, but you've taken a contrarian stance. You actually think AI is underhyped. Why is that?](#01:35)\n[**ES:** Most of you think of AI as, I'll just use the general term, as ChatGPT.](#01:50)\n[For most of you, ChatGPT was the moment where you said, \"Oh my God, this thing writes, and it makes mistakes, but it's so brilliantly verbal.\" That was certainly my reaction, and most people that I knew reacted similarly. Since then, the gains in what is called reinforcement learning, which is what AlphaGo helped invent, allow us to do planning. A good example is look at OpenAI o3 or DeepSeek R1, and you can see how it goes forward and back, forward and back. It's extraordinary.](#01:55)\n[In my case, I bought a rocket company because it was interesting, and it’s an area that I’m not an expert in, and I want to be an expert. So I'm using deep research, and these systems are spending 15 minutes writing these deep papers. That's true for most of them. Do you have any idea how much computation 15 minutes of these supercomputers is? It's extraordinary.](#02:31)\n[So you’re seeing the shift from language to language, then you had language to sequence, which is how biology is done. Now you're doing essentially planning and strategy. The eventual state of this is the computers running all business processes, right? So you have an agent to do this, an agent to do this, an agent to do this. And you concatenate them together, and they speak language among each other. They typically speak English language.](#02:57)\n[Let's talk about scale briefly. You know, I kind of think of these AI systems as Hungry Hungry Hippos. They seemingly soak up all the data and compute that we throw at them. They've already digested all the tokens on the public internet, and it seems we can't build data centers fast enough. What do you think the real limits are, and how do we get ahead of them before they start throttling AI progress?](#03:30)\n[Give you an example. There's one calculation, and I testified on this this week in Congress, that we need another 90 gigawatts of power in America. My answer, by the way, is, think Canada, right? Nice people, full of hydroelectric power. But that's apparently not the political mood right now. Sorry. So 90 gigawatts is 90 nuclear power plants in America. Not happening. We're building zero, right? How are we going to get all that power? This is a major, major national issue.](#03:55)\n[You can use the Arab world, which is busy building five to 10 gigawatts of data centers. India is considering a 10-gigawatt data center. To understand how big gigawatts are, is think cities per data center. That's how much power these things need. And the people look at it and they say, “Well, there’s lots of algorithmic improvements, and you will need less power.\" There's an old rule, I'm old enough to remember, right? Grove giveth, Gates taketh away. OK, the hardware just gets faster and faster. The physicists are amazing, just incredible what they've been able to do. And us software people, we just use it and use it and use it.](#04:30)\n[And when you look at planning, at least in today's algorithms, it's back and forth and try this and that and just watch it yourself. There are estimates, and you know this from Andreessen Horowitz reports, it's been well studied, that there's an increase in at least a factor of 100, maybe a factor of 1,000, in computation required just to do the kind of planning. The technology goes from essentially deep learning to reinforcement learning to something called test-time compute, where not only are you doing planning, but you're also learning while you're doing planning. That is the zenith of computation needs. That's problem number one, electricity and hardware.](#05:11)\n[Problem number two is we ran out of data, so we have to start generating it. But we can easily do that because that's one of the functions. And then the third question that I don't understand is what's the limit of knowledge? I'll give you an example. Let's imagine we are collectively all of the computers in the world, and we're all thinking and we're all thinking based on knowledge that exists that was previously invented. How do we invent something completely new? So, Einstein.](#05:50)\n[So when you study the way scientific discovery works, biology, math, so forth and so on, what typically happens is a truly brilliant human being looks at one area and says, \"I see a pattern that's in a completely different area, has nothing to do with the first one. It's the same pattern.\" And they take the tools from one and they apply it to another. Today, our systems cannot do that. If we can get through that, I'm working on this, a general technical term for this is non-stationarity of objectives. The rules keep changing.](#06:22)\n\n[We will see if we can solve that problem. If we can solve that, we're going to need even more data centers, and we'll also be able to invent completely new schools of scientific and intellectual thought, which will be incredible.](#06:59)\n[Autonomy has been a big topic of discussion. Yoshua Bengio gave a compelling talk earlier this week, advocating that AI labs should halt the development of agentic AI systems that are capable of taking autonomous action. Yet that is precisely what the next frontier is for all these AI labs, and seemingly for yourself, too. What is the right decision here?](#07:12)\n[And a good personal friend. And we’ve talked about this, and his concerns are very legitimate. The question is not are his concerns right, but what are the solutions? So let's think about agents. For purposes of argument, everyone in the audience is an agent. You have an input that's English or whatever language, and you have an output that’s English, and you have memory, which is true of all humans.](#07:34)\n[Now we're all busy working, and all of a sudden, one of you decides it's much more efficient not to use human language, but we'll invent our own computer language. Now you and I are sitting here, watching all of this, and we're saying, like, what do we do now? The correct answer is unplug you, right? Because we're not going to know, we're just not going to know what you're up to. And you might actually be doing something really bad or really amazing.](#08:00)\n[We want to be able to watch. So we need provenance, something you and I have talked about, but we also need to be able to observe it. To me, that's a core requirement. There's a set of criteria that the industry believes are points where you want to, metaphorically, unplug it. One is where you get recursive self-improvement, which you can't control. Recursive self-improvement is where the computer is off learning, and you don't know what it's learning. That can obviously lead to bad outcomes.](#08:28)\n[Another one would be direct access to weapons. Another one would be that the computer systems decide to exfiltrate themselves, to reproduce themselves without our permission. So there's a set of such things. The problem with Yoshua's speech, with respect to such a brilliant person, is stopping things in a globally competitive market doesn't really work. Instead of stopping agentic work, we need to find a way to establish the guardrails, which I know you agree with because we’ve talked about it.](#08:53)\n[And let's just say there are a lot of them when it comes to this technology. The first one I'd love to start with, Eric, is the exceedingly dual-use nature of this tech, right? It's applicable to both civilian and military applications. So how do you broadly think about the dilemmas and ethical quandaries that come with this tech and how humans deploy them?](#09:30)\n[about personal responsibility. A simple example, I did a lot of military work and continue to do so. The US military has a rule called 3000.09, generally known as \"human in the loop\" or \"meaningful human control.\" You don't want systems that are not under our control. It's a line we can't cross. I think that's correct. I think that the competition between the West, and particularly the United States, and China, is going to be defining in this area.](#09:53)\n[And I'll give you some examples. First, the current government has now put in essentially reciprocating 145-percent tariffs. That has huge implications for the supply chain. We in our industry depend on packaging and components from China that are boring, if you will, but incredibly important. The little packaging and the little glue things and so forth that are part of the computers. If China were to deny access to them, that would be a big deal. We are trying to deny them access to the most advanced chips, which they are super annoyed about.](#10:26)\n[Dr. Kissinger asked Craig and I to do Track II dialogues with the Chinese, and we’re in conversations with them. What's the number one issue they raise? This issue. Indeed, if you look at DeepSeek, which is really impressive, they managed to find algorithms that got around the problems by making them more efficient. Because China is doing everything open source, open weights, we immediately got the benefit of their invention and have adopted into US things.](#11:00)\n[So we're in a situation now which I think is quite tenuous, where the US is largely driving, for many, many good reasons, largely closed models, largely under very good control. China is likely to be the leader in open source unless something changes. And open source leads to very rapid proliferation around the world. This proliferation is dangerous at the cyber level and the bio level. But let me give you why it's also dangerous in a more significant way, in a nuclear-threat way.](#11:26)\n[Dr. Kissinger, who we all worked with very closely, was one of the architects of mutually assured destruction and deterrence. What's happening now is you've got a situation where -- I'll use an example; it's easier if I explain. You’re the good guy, and I’m the bad guy, OK? You're six months ahead of me, and we're both on the same path for superintelligence. And you're going to get there, right? And I'm sure you're going to get there, you're that close. And I'm six months behind.](#11:56)\n[Pretty good, right? Sounds pretty good. No. These are network-effect businesses. And in network-effect businesses, it is the slope of your improvement that determines everything. So I'll use OpenAI or Gemini, they have 1,000 programmers. They're in the process of creating a million AI software programmers. What does that do? First, you don't have to feed them except electricity. So that's good. And they don't quit and things like that. Second, the slope is like this. Well, as we get closer to superintelligence, the slope goes like this.](#12:25)\n[If you get there first, you dastardly person -- And I've given you the tools to reinvent the world and, in particular, destroy me. That's how my brain, Mr. Evil, is going to think. So what am I going to do? The first thing I'm going to do is try to steal all your code, but you've prevented that because you're good. So you’re still good, at Google. Second, then I'm going to infiltrate you with humans, but you've got good protections against that. So what do I do?](#13:01)\n[I’m going to go in, and I’m going to change your model. I'm going to modify it. I'm going to actually screw you up to get me so I'm one day ahead of you. And you're so good, I can't do that. What's my next choice? Bomb your data center. Now do you think I’m insane? These conversations are occurring around nuclear opponents today in our world. There are legitimate people saying the only solution to this problem is preemption.](#13:33)\n[Now I just told you that you, Mr. Good, are about to have the keys to control the entire world, both in terms of economic dominance, innovation, surveillance, whatever it is that you care about. I have to prevent that. We don't have any language in our society, the foreign policy people have not thought about this, and this is coming. When is it coming? Probably five years. We have time. We have time for this conversation, and this is really important.](#13:50)\n[So if this is true and we can end up in this sort of standoff scenario and the equivalent of mutually-assured destruction, you've also said that the US should embrace open-source AI even after China's DeepSeek showed what's possible with a fraction of the compute. But doesn't open-sourcing these models just hand capabilities to adversaries that will accelerate their own timelines?](#14:38)\n[Our industry, our science, everything about the world that we have built is based on academic research, open source, and so forth. Much of Google's technology was based on open source. Some of Google's technology is open-source, some of it is proprietary, perfectly legitimate. What happens when there's an open-source model that is really dangerous, and it gets into the hands of the Osama bin Ladens of the world, and we know there are more than one, unfortunately? We don't know.](#15:02)\n[The consensus in the industry right now is the open-source models are not quite at the point of national or global danger, but you can see a pattern where they might get there. So a lot will now depend upon the key decisions made in the US and China and in the companies in both places. The reason I focus on US and China is they're the only two countries where people are crazy enough to spend the billions and billions of dollars that are required to build this new vision.](#15:30)\n[Europe, which would love to do it, doesn't have the capital structure to do it. Most of the other countries, not even India, has the capital structure to do it, although they wish to. Arabs don't have the capital structure to do it, although they're working on it. So this fight, this battle, will be the defining battle. I'm worried about this fight.](#16:00)\n[Dr. Kissinger talked about the likely path to war with China was by accident, and he was a student of World War I. And of course, it started with a small event, and it escalated over that summer in, I think, 1914. And then it was this horrific conflagration. You can imagine a series of steps along the lines of what I'm talking about that could lead us to a horrific global outcome. That's why we have to be paying attention.](#16:18)\n[Before we move on, is to sort of moderate these AI systems at scale, right? There's this weird tension in AI safety that the solution to preventing \"1984\" often sounds a lot like \"1984.\" So proof of personhood is a hot topic, and moderating these systems at scale is a hot topic. How do you view that trade-off? In trying to prevent dystopia, let's say preventing non-state actors from using these models in undesirable ways, we might accidentally end up building the ultimate surveillance state.](#16:53)\n\n[I am very, very committed to individual freedom. It's very easy for a well-intentioned engineer to build a system which is optimized and restricts your freedom. So it's very important that human freedom be preserved in this. A lot of these are not technical issues; they're really business decisions. It's certainly possible to build a surveillance state, but it's also possible to build one that's freeing. The conundrum that you're describing is because it's now so easy to operate based on misinformation, that you really do need proof of identity. But proof of identity does not have to include details. So, for example, you could have a cryptographic proof that you are a human being, and it could actually be true without anything else, and also not be able to link it to others using various cryptographic techniques.](#17:27)\n\n[In your book, \"Genesis,\" you strike a cautiously optimistic tone, which you obviously co-authored with Henry Kissinger. When you look ahead to the future, what should we all be excited about?](#18:26)\n\n[Can we fix that now? Can we just eliminate all of those? Why can't we just uptake these and right now, eradicate all of these diseases? That's a pretty good goal. I'm aware of one nonprofit that's trying to identify, in the next two years, all human druggable targets and release it to the scientists. If you know the druggable targets, then the drug industry can begin to work on things. I have another company I'm associated with which has figured out a way, allegedly, it's a startup, to reduce the cost of stage-3 trials by an order of magnitude. As you know, those are the things that ultimately drive the cost structure of drugs. That's an example.](#18:36)\n\n[I'd like to know where dark energy is, and I'd like to find it. I'm sure that there is an enormous amount of physics in dark energy, dark matter. Think about the revolution in material science, infinitely more powerful transportation, infinitely more powerful science and so forth. I'll give you another example. Why do we not have every human being on the planet have their own tutor in their own language to help them learn something new, starting with kindergarten? It's obvious. Why have we not built it? The answer, the only possible answer is there must not be a good economic argument. The technology works. Teach them in their language, gamify the learning, bring people to their best natural lengths.](#19:21)\n\n[Another example. The vast majority of health care in the world is either absent or delivered by the equivalent of nurse practitioners and very, very sort of stressed local village doctors. Why do they not have the doctor assistant that helps them in their language, treat whatever with, again, perfect healthcare? I can just go on. There are lots and lots of issues with the digital world. It feels like that we're all in our own ships in the ocean, and we're not talking to each other. In our hunger for connectivity and connection, these tools make us lonelier. We've got to fix that, right? But these are fixable problems. They don't require new physics. They don't require new discoveries, we just have to decide.](#20:11)\n\n[So when I look at this future, I want to be clear that the arrival of this intelligence, both at the AI level, the AGI, which is general intelligence, and then superintelligence, is the most important thing that's going to happen in about 500 years, maybe 1,000 years in human society. And it's happening in our lifetime. So don't screw it up.](#20:55)\n\n[(Applause) Let's say we don't screw it up. Let's say we get into this world of radical abundance. Let's say we end up in this place, and we hit that point of recursive self-improvement. AI systems take on a vast majority of economically productive tasks. In your mind, what are humans going to do in this future? Are we all sipping piña coladas on the beach, engaging in hobbies? You must be in favor of UBI. In the midst of this incredible discovery, do you really think that we're going to get rid of lawyers? No, they're just going to have more sophisticated lawsuits.](#21:21)\n\n[Do you really think we're going to get rid of politicians? No, they'll just have more platforms to mislead you. Sorry. I mean, I can just go on and on and on.](#22:01)\n\n[The key thing to understand about this new economics is that we collectively, as a society, are not having enough humans. Look at the reproduction rate in Asia; it's essentially 1.0 for two parents. This is not good, right? So for the rest of our lives, the key problem is going to get the people who are productive—that is, in their productive period of lives—more productive to support old people like me, right, who will be bitching that we want more stuff from the younger people. That's how it's going to work.](#22:10)\n\n[These tools will radically increase that productivity. There's a study that says that we will, under this set of assumptions around agentic AI and discovery and the scale that I'm describing—there's a lot of assumptions—that you'll end up with something like a 30-percent increase in productivity per year. Having now talked to a bunch of economists, they have no models for what that kind of increase in productivity looks like. We just have never seen it. It didn't occur in any rise of a democracy or a kingdom in our history. It's unbelievable what's going to happen. Hopefully, we will get it in the right direction.](#22:42)\n\n[Let's bring this home, Eric. You've navigated decades of technological change. For everyone that's navigating this AI transition—technologists, leaders, citizens that are feeling a mix of excitement and anxiety—what is that single piece of wisdom or advice you'd like to offer for navigating this insane moment that we're living through today?](#23:24)\n\n[This is a marathon, not a sprint. One year I decided to do a 100-mile bike race, which was a mistake. And the idea was, I learned about spin rate. Every day, you get up, and you just keep going. You know, from our work together at Google, that when you’re growing at the rate that we’re growing, you get so much done in a year, you forget how far you went. Humans can't understand that. And we're in this situation where the exponential is moving like this. As this stuff happens quicker, you will forget what was true two years ago or three years ago. That's the key thing.](#23:44)\n\n[So my advice to you all is ride the wave, but ride it every day. Don't view it as episodic and something you can end, but understand it and build on it. Each and every one of you has a reason to use this technology—if you're an artist, a teacher, a physician, a business person, a technical person. If you're not using this technology, you're not going to be relevant compared to your peer groups and your competitors and the people who want to be successful. Adopt it, and adopt it fast.](#24:26)\n\n[I have been shocked at how fast these systems -- as an aside, my background is enterprise software, and nowadays there's a model Protocol from Anthropic. You can actually connect the model directly into the databases without any of the connectors. I know this sounds nerdy. There's a whole industry there that goes away because you have all this flexibility now. You can just say what you want, and it just produces it. That's an example of a real change in business. There are so many of these things coming every day.](#24:57)\n",
          format: 'subtitle-formatted',
          raw: 'raw-transcript',
          language: 'en-US',
          status: 'completed',
        },
        {
          id: '01980bdb-fae2-7068-9aa4-81e5fd78fce5',
          block_id: '01980bdb-fadf-7529-b253-5e52d8f9bf77',
          raw: 'raw-transcript',
          plain:
            "0:05: Bilawal Sidhu: Eric Schmidt, thank you for joining us.\n0:08: Let's go back.\n0:10: You said the arrival of non-human intelligence is a very big deal.\n0:14: And this photo, taken in 2016,\n0:17: feels like one of those quiet moments where the Earth shifted beneath us,\n0:21: but not everyone noticed.\n0:23: What did you see back then that the rest of us might have missed?\n0:26: Eric Schmidt: In 2016, we didn't understand\n0:29: what was now going to happen,\n0:30: but we understood that these algorithms were new and powerful.\n0:34: What happened in this particular set of games\n0:37: was in roughly the second game,\n0:39: there was a new move invented by AI\n0:42: in a game that had been around for 2,500 years\n0:45: that no one had ever seen.\n0:47: Technically, the way this occurred\n0:49: was that the system of AlphaGo was essentially organized\n0:52: to always maintain a greater than 50 percent chance of winning.\n0:56: And so it calculated correctly this move,\n0:59: which was this great mystery among all of the Go players\n1:02: who are obviously insanely brilliant,\n1:04: mathematical and intuitive players.\n1:07: The question that Henry, Craig Mundie and I started to discuss, right,\n1:14: is what does this mean?\n1:18: How is it that our computers could come up with something\n1:21: that humans had never thought about?\n1:23: I mean, this is a game played by billions of people.\n1:26: And that began the process that led to two books.\n1:30: And I think, frankly,\n1:31: is the point at which the revolution really started.\n1:35: BS: If you fast forward to today,\n1:38: it seems that all anyone can talk about is AI,\n1:42: especially here at TED.\n1:44: But you've taken a contrarian stance.\n1:46: You actually think AI is underhyped.\n1:49: Why is that?\n1:50: ES: And I'll tell you why.\n1:51: Most of you think of AI as,\n1:53: I'll just use the general term, as ChatGPT.\n1:55: For most of you, ChatGPT was the moment where you said,\n1:58: \"Oh my God,\n1:59: this thing writes, and it makes mistakes,\n2:01: but it's so brilliantly verbal.\"\n2:04: That was certainly my reaction.\n2:05: Most people that I knew did that.\n2:07: BS: It was visceral, yeah.\n2:08: ES: This was two years ago.\n2:10: Since then, the gains in what is called reinforcement learning,\n2:14: which is what AlphaGo helped invent and so forth,\n2:17: allow us to do planning.\n2:19: And a good example is look at OpenAI o3\n2:24: or DeepSeek R1,\n2:26: and you can see how it goes forward and back,\n2:28: forward and back, forward and back.\n2:31: It's extraordinary.\n2:32: In my case, I bought a rocket company\n2:35: because it was like, interesting.\n2:37: BS: (Laughs) As one does.\n2:38: ES: As one does.\n2:39: And it’s an area that I’m not an expert in,\n2:42: and I want to be an expert.\n2:43: So I'm using deep research.\n2:45: And these systems are spending 15 minutes writing these deep papers.\n2:50: That's true for most of them.\n2:51: Do you have any idea how much computation\n2:54: 15 minutes of these supercomputers is?\n2:56: It's extraordinary.\n2:58: So you’re seeing the arrival,\n2:60: the shift from language to language.\n3:02: Tthen you had language to sequence,\n3:04: which is how biology is done.\n3:05: Now you're doing essentially planning and strategy.\n3:09: The eventual state of this\n3:12: is the computers running all business processes, right?\n3:15: So you have an agent to do this, an agent to do this,\n3:17: an agent to do this.\n3:19: And you concatenate them together,\n3:21: and they speak language among each other.\n3:23: They typically speak English language.\n3:26: BS: I mean, speaking of just the sheer compute requirements of these systems,\n3:31: let's talk about scale briefly.\n3:33: You know, I kind of think of these AI systems as Hungry Hungry Hippos.\n3:37: They seemingly soak up all the data and compute that we throw at them.\n3:40: They've already digested all the tokens on the public internet,\n3:44: and it seems we can't build data centers fast enough.\n3:47: What do you think the real limits are,\n3:49: and how do we get ahead of them\n3:51: before they start throttling AI progress?\n3:54: ES: So there's a real limit in energy.\n3:56: Give you an example.\n3:57: There's one calculation,\n3:58: and I testified on this this week in Congress,\n4:01: that we need another 90 gigawatts of power in America.\n4:07: My answer, by the way, is, think Canada, right?\n4:11: Nice people, full of hydroelectric power.\n4:13: But that's apparently not the political mood right now.\n4:16: Sorry.\n4:17: So 90 gigawatts is 90 nuclear power plants in America.\n4:23: Not happening.\n4:24: We're building zero, right?\n4:26: How are we going to get all that power?\n4:28: This is a major, major national issue.\n4:31: You can use the Arab world,\n4:32: which is busy building five to 10 gigawatts of data centers.\n4:36: India is considering a 10-gigawatt data center.\n4:39: To understand how big gigawatts are,\n4:41: is think cities per data center.\n4:44: That's how much power these things need.\n4:46: And the people look at it and they say,\n4:49: “Well, there’s lots of algorithmic improvements,\n4:52: and you will need less power.\"\n4:53: There's an old rule, I'm old enough to remember, right?\n4:57: Grove giveth, Gates taketh away.\n5:01: OK, the hardware just gets faster and faster.\n5:04: The physicists are amazing.\n5:06: Just incredible what they've been able to do.\n5:08: And us software people, we just use it and use it and use it.\n5:12: And when you look at planning, at least in today's algorithms,\n5:16: it's back and forth and try this and that\n5:19: and just watch it yourself.\n5:20: There are estimates, and you know this from Andreessen Horowitz reports,\n5:25: it's been well studied,\n5:27: that there's an increase in at least a factor of 100,\n5:30: maybe a factor of 1,000,\n5:31: in computation required just to do the kind of planning.\n5:35: The technology goes from essentially deep learning to reinforcement learning\n5:39: to something called test-time compute,\n5:41: where not only are you doing planning,\n5:43: but you're also learning while you're doing planning.\n5:45: That is the, if you will,\n5:46: the zenith or what have you, of computation needs.\n5:50: That's problem number one, electricity and hardware.\n5:53: Problem number two is we ran out of data\n5:57: so we have to start generating it.\n5:59: But we can easily do that because that's one of the functions.\n6:02: And then the third question that I don't understand\n6:05: is what's the limit of knowledge?\n6:07: I'll give you an example.\n6:09: Let's imagine we are collectively all of the computers in the world,\n6:12: and we're all thinking\n6:13: and we're all thinking based on knowledge that exists that was previously invented.\n6:17: How do we invent something completely new?\n6:22: So, Einstein.\n6:23: So when you study the way scientific discovery works,\n6:26: biology, math, so forth and so on,\n6:29: what typically happens is a truly brilliant human being\n6:32: looks at one area and says,\n6:35: \"I see a pattern\n6:37: that's in a completely different area,\n6:39: has nothing to do with the first one.\n6:41: It's the same pattern.\"\n6:42: And they take the tools from one and they apply it to another.\n6:46: Today, our systems cannot do that.\n6:48: If we can get through that, I'm working on this,\n6:51: a general technical term for this is non-stationarity of objectives.\n6:56: The rules keep changing.\n6:59: We will see if we can solve that problem.\n7:01: If we can solve that, we're going to need even more data centers.\n7:04: And we'll also be able to invent completely new schools of scientific\n7:09: and intellectual thought,\n7:10: which will be incredible.\n7:12: BS: So as we push towards a zenith,\n7:14: autonomy has been a big topic of discussion.\n7:16: Yoshua Bengio gave a compelling talk earlier this week,\n7:19: advocating that AI labs should halt the development of agentic AI systems\n7:23: that are capable of taking autonomous action.\n7:26: Yet that is precisely what the next frontier is for all these AI labs,\n7:30: and seemingly for yourself, too.\n7:32: What is the right decision here?\n7:34: ES: So Yoshua is a brilliant inventor of much of what we're talking about\n7:38: and a good personal friend.\n7:40: And we’ve talked about this, and his concerns are very legitimate.\n7:44: The question is not are his concerns right,\n7:46: but what are the solutions?\n7:47: So let's think about agents.\n7:50: So for purposes of argument, everyone in the audience is an agent.\n7:53: You have an input that's English or whatever language.\n7:57: And you have an output that’s English, and you have memory,\n7:60: which is true of all humans.\n8:01: Now we're all busy working,\n8:03: and all of a sudden, one of you decides\n8:07: it's much more efficient not to use human language,\n8:10: but we'll invent our own computer language.\n8:12: Now you and I are sitting here, watching all of this,\n8:14: and we're saying, like, what do we do now?\n8:17: The correct answer is unplug you, right?\n8:20: Because we're not going to know,\n8:23: we're just not going to know what you're up to.\n8:25: And you might actually be doing something really bad or really amazing.\n8:29: We want to be able to watch.\n8:30: So we need provenance, something you and I have talked about,\n8:34: but we also need to be able to observe it.\n8:36: To me, that's a core requirement.\n8:39: There's a set of criteria that the industry believes are points\n8:42: where you want to, metaphorically, unplug it.\n8:45: One is where you get recursive self-improvement,\n8:47: which you can't control.\n8:48: Recursive self-improvement is where the computer is off learning,\n8:51: and you don't know what it's learning.\n8:53: That can obviously lead to bad outcomes.\n8:55: Another one would be direct access to weapons.\n8:57: Another one would be that the computer systems decide to exfiltrate themselves,\n9:02: to reproduce themselves without our permission.\n9:05: So there's a set of such things.\n9:07: The problem with Yoshua's speech, with respect to such a brilliant person,\n9:12: is stopping things in a globally competitive market\n9:15: doesn't really work.\n9:17: Instead of stopping agentic work,\n9:21: we need to find a way to establish the guardrails,\n9:23: which I know you agree with because we’ve talked about it.\n9:26: (Applause)\n9:30: BS: I think that brings us nicely to the dilemmas.\n9:32: And let's just say there are a lot of them when it comes to this technology.\n9:36: The first one I'd love to start with, Eric,\n9:38: is the exceedingly dual-use nature of this tech, right?\n9:41: It's applicable to both civilian and military applications.\n9:45: So how do you broadly think about the dilemmas\n9:47: and ethical quandaries\n9:49: that come with this tech and how humans deploy them?\n9:53: ES: In many cases, we already have doctrines\n9:56: about personal responsibility.\n9:58: A simple example, I did a lot of military work\n9:60: and continue to do so.\n10:01: The US military has a rule called 3000.09,\n10:05: generally known as \"human in the loop\" or \"meaningful human control.\"\n10:10: You don't want systems that are not under our control.\n10:14: It's a line we can't cross.\n10:16: I think that's correct.\n10:17: I think that the competition between the West,\n10:21: and particularly the United States,\n10:22: and China,\n10:24: is going to be defining in this area.\n10:26: And I'll give you some examples.\n10:28: First, the current government has now put in\n10:31: essentially reciprocating 145-percent tariffs.\n10:35: That has huge implications for the supply chain.\n10:38: We in our industry depend on packaging\n10:41: and components from China that are boring, if you will,\n10:45: but incredibly important.\n10:46: The little packaging and the little glue things and so forth\n10:49: that are part of the computers.\n10:51: If China were to deny access to them, that would be a big deal.\n10:54: We are trying to deny them access to the most advanced chips,\n10:58: which they are super annoyed about.\n11:01: Dr. Kissinger asked Craig and I\n11:03: to do Track II dialogues with the Chinese,\n11:06: and we’re in conversations with them.\n11:08: What's the number one issue they raise?\n11:10: This issue.\n11:11: Indeed, if you look at DeepSeek, which is really impressive,\n11:14: they managed to find algorithms that got around the problems\n11:17: by making them more efficient.\n11:19: Because China is doing everything open source, open weights,\n11:22: we immediately got the benefit of their invention\n11:25: and have adopted into US things.\n11:27: So we're in a situation now which I think is quite tenuous,\n11:31: where the US is largely driving, for many, many good reasons,\n11:34: largely closed models, largely under very good control.\n11:38: China is likely to be the leader in open source unless something changes.\n11:42: And open source leads to very rapid proliferation around the world.\n11:46: This proliferation is dangerous at the cyber level and the bio level.\n11:50: But let me give you why it's also dangerous in a more significant way,\n11:54: in a nuclear-threat way.\n11:56: Dr. Kissinger, who we all worked with very closely,\n11:59: was one of the architects of mutually assured destruction,\n12:02: deterrence and so forth.\n12:03: And what's happening now is you've got a situation\n12:07: where -- I'll use an example.\n12:08: It's easier if I explain.\n12:09: You’re the good guy, and I’m the bad guy, OK?\n12:12: You're six months ahead of me,\n12:14: and we're both on the same path for superintelligence.\n12:17: And you're going to get there, right?\n12:19: And I'm sure you're going to get there, you're that close.\n12:23: And I'm six months behind.\n12:25: Pretty good, right?\n12:27: Sounds pretty good.\n12:29: No.\n12:30: These are network-effect businesses.\n12:32: And in network-effect businesses,\n12:34: it is the slope of your improvement that determines everything.\n12:38: So I'll use OpenAI or Gemini,\n12:40: they have 1,000 programmers.\n12:42: They're in the process of creating a million AI software programmers.\n12:46: What does that do?\n12:48: First, you don't have to feed them except electricity.\n12:50: So that's good.\n12:52: And they don't quit and things like that.\n12:54: Second, the slope is like this.\n12:56: Well, as we get closer to superintelligence,\n12:59: the slope goes like this.\n13:01: If you get there first, you dastardly person --\n13:04: BS: You're never going to be able to catch me.\n13:06: ES: I will not be able to catch you.\n13:08: And I've given you the tools\n13:09: to reinvent the world and in particular, destroy me.\n13:13: That's how my brain, Mr. Evil, is going to think.\n13:16: So what am I going to do?\n13:18: The first thing I'm going to do is try to steal all your code.\n13:21: And you've prevented that because you're good.\n13:23: And you were good.\n13:25: So you’re still good, at Google.\n13:27: Second, then I'm going to infiltrate you with humans.\n13:30: Well, you've got good protections against that.\n13:32: You know, we don't have spies.\n13:33: So what do I do?\n13:36: I’m going to go in, and I’m going to change your model.\n13:38: I'm going to modify it.\n13:40: I'm going to actually screw you up\n13:41: to get me so I'm one day ahead of you.\n13:44: And you're so good, I can't do that.\n13:45: What's my next choice?\n13:48: Bomb your data center.\n13:50: Now do you think I’m insane?\n13:54: These conversations are occurring\n13:56: around nuclear opponents today in our world.\n14:00: There are legitimate people saying\n14:02: the only solution to this problem is preemption.\n14:06: Now I just told you that you, Mr. Good,\n14:09: are about to have the keys to control the entire world,\n14:13: both in terms of economic dominance,\n14:15: innovation, surveillance,\n14:17: whatever it is that you care about.\n14:19: I have to prevent that.\n14:21: We don't have any language in our society,\n14:24: the foreign policy people have not thought about this,\n14:27: and this is coming.\n14:28: When is it coming?\n14:30: Probably five years.\n14:32: We have time.\n14:33: We have time for this conversation.\n14:34: And this is really important.\n14:36: BS: Let me push on this a little bit.\n14:38: So if this is true\n14:39: and we can end up in this sort of standoff scenario\n14:41: and the equivalent of mutually-assured destruction,\n14:44: you've also said that the US should embrace open-source AI\n14:48: even after China's DeepSeek showed what's possible\n14:50: with a fraction of the compute.\n14:51: But doesn't open-sourcing these models,\n14:53: just hand capabilities to adversaries that will accelerate their own timelines?\n14:58: ES: This is one of the wickedest, or, we call them wicked hard problems.\n15:02: Our industry, our science,\n15:04: everything about the world that we have built\n15:07: is based on academic research, open source, so forth.\n15:10: Much of Google's technology was based on open source.\n15:13: Some of Google's technology is open-source,\n15:15: some of it is proprietary, perfectly legitimate.\n15:18: What happens when there's an open-source model\n15:22: that is really dangerous,\n15:23: and it gets into the hands of the Osama bin Ladens of the world,\n15:27: and we know there are more than one, unfortunately.\n15:30: We don't know.\n15:31: The consensus in the industry right now\n15:33: is the open-source models are not quite at the point\n15:38: of national or global danger.\n15:41: But you can see a pattern where they might get there.\n15:44: So a lot will now depend upon the key decisions made in the US and China\n15:49: and in the companies in both places.\n15:51: The reason I focus on US and China\n15:53: is they're the only two countries where people are crazy enough\n15:57: to spend the billions and billions of dollars\n15:60: that are required to build this new vision.\n16:02: Europe, which would love to do it,\n16:03: doesn't have the capital structure to do it.\n16:05: Most of the other countries, not even India,\n16:07: has the capital structure to do it, although they wish to.\n16:10: Arabs don't have the capital structure to do it,\n16:13: although they're working on it.\n16:14: So this fight, this battle, will be the defining battle.\n16:18: I'm worried about this fight.\n16:20: Dr. Kissinger talked about the likely path to war with China\n16:25: was by accident.\n16:28: And he was a student of World War I.\n16:30: And of course, [it] started with a small event,\n16:32: and it escalated over that summer in, I think, 1914.\n16:36: And then it was this horrific conflagration.\n16:39: You can imagine a series of steps\n16:41: along the lines of what I'm talking about\n16:44: that could lead us to a horrific global outcome.\n16:47: That's why we have to be paying attention.\n16:50: BS: I want to talk about one of the recurring tensions here,\n16:53: before we move on to the dreams,\n16:55: is, to sort of moderate these AI systems at scale, right,\n16:58: there's this weird tension in AI safety\n16:60: that the solution to preventing \"1984\"\n17:03: often sounds a lot like \"1984.\"\n17:06: So proof of personhood is a hot topic.\n17:08: Moderating these systems at scale is a hot topic.\n17:10: How do you view that trade-off?\n17:12: In trying to prevent dystopia,\n17:14: let's say preventing non-state actors\n17:16: from using these models in undesirable ways,\n17:18: we might accidentally end up building the ultimate surveillance state.\n17:23: ES: It's really important that we stick to the values\n17:27: that we have in our society.\n17:29: I am very, very committed to individual freedom.\n17:32: It's very easy for a well-intentioned engineer to build a system\n17:36: which is optimized and restricts your freedom.\n17:40: So it's very important that human freedom be preserved in this.\n17:44: A lot of these are not technical issues.\n17:46: They're really business decisions.\n17:48: It's certainly possible to build a surveillance state,\n17:51: but it's also possible to build one that's freeing.\n17:53: The conundrum that you're describing\n17:55: is because it's now so easy to operate based on misinformation,\n17:58: everyone knows what I'm talking about,\n18:00: that you really do need proof of identity.\n18:03: But proof of identity does not have to include details.\n18:06: So, for example, you could have a cryptographic proof\n18:08: that you are a human being,\n18:09: and it could actually be true without anything else,\n18:12: and also not be able to link it to others\n18:15: using various cryptographic techniques.\n18:17: BS: So zero-knowledge proofs and other techniques.\n18:20: ES: Zero-knowledge proofs are the most obvious one.\n18:22: BS: Alright, let's change gears, shall we, to dreams.\n18:26: In your book, \"Genesis,\" you strike a cautiously optimistic tone,\n18:29: which you obviously co-authored with Henry Kissinger.\n18:32: When you look ahead to the future, what should we all be excited about?\n18:36: ES: Well, I'm of the age\n18:37: where some of my friends are getting really dread diseases.\n18:41: Can we fix that now?\n18:44: Can we just eliminate all of those?\n18:46: Why can't we just uptake these\n18:47: and right now, eradicate all of these diseases?\n18:52: That's a pretty good goal.\n18:55: I'm aware of one nonprofit that's trying to identify,\n18:58: in the next two years,\n18:59: all human druggable targets and release it to the scientists.\n19:03: If you know the druggable targets,\n19:05: then the drug industry can begin to work on things.\n19:08: I have another company I'm associated with\n19:10: which has figured out a way, allegedly, it's a startup,\n19:13: to reduce the cost of stage-3 trials by an order of magnitude.\n19:17: As you know, those are the things\n19:18: that ultimately drive the cost structure of drugs.\n19:21: That's an example.\n19:22: I'd like to know where dark energy is,\n19:25: and I'd like to find it.\n19:27: I'm sure that there is an enormous amount of physics in dark energy, dark matter.\n19:32: Think about the revolution in material science.\n19:36: Infinitely more powerful transportation,\n19:39: infinitely more powerful science and so forth.\n19:42: I'll give you another example.\n19:44: Why do we not have every human being on the planet\n19:50: have their own tutor in their own language\n19:53: to help them learn something new?\n19:55: Starting with kindergarten.\n19:57: It's obvious.\n19:58: Why have we not built it?\n19:60: The answer, the only possible answer\n20:01: is there must not be a good economic argument.\n20:04: The technology works.\n20:05: Teach them in their language, gamify the learning,\n20:08: bring people to their best natural lengths.\n20:11: Another example.\n20:12: The vast majority of health care in the world\n20:14: is either absent\n20:15: or delivered by the equivalent of nurse practitioners\n20:18: and very, very sort of stressed local village doctors.\n20:21: Why do they not have the doctor assistant that helps them in their language,\n20:25: treat whatever with, again, perfect healthcare?\n20:28: I can just go on.\n20:30: There are lots and lots of issues with the digital world.\n20:35: It feels like that we're all in our own ships in the ocean,\n20:39: and we're not talking to each other.\n20:41: In our hunger for connectivity and connection,\n20:44: these tools make us lonelier.\n20:47: We've got to fix that, right?\n20:49: But these are fixable problems.\n20:50: They don't require new physics.\n20:52: They don't require new discoveries, we just have to decide.\n20:55: So when I look at this future,\n20:57: I want to be clear that the arrival of this intelligence,\n21:01: both at the AI level, the AGI,\n21:04: which is general intelligence,\n21:05: and then superintelligence,\n21:07: is the most important thing that's going to happen in about 500 years,\n21:12: maybe 1,000 years in human society.\n21:14: And it's happening in our lifetime.\n21:16: So don't screw it up.\n21:19: BS: Let's say we don't.\n21:21: (Applause)\n21:24: Let's say we don't screw it up.\n21:25: Let's say we get into this world of radical abundance.\n21:28: Let's say we end up in this place,\n21:30: and we hit that point of recursive self-improvement.\n21:34: AI systems take on a vast majority of economically productive tasks.\n21:38: In your mind, what are humans going to do in this future?\n21:41: Are we all sipping piña coladas on the beach, engaging in hobbies?\n21:44: ES: You tech liberal, you.\n21:46: You must be in favor of UBI.\n21:48: BS: No, no, no.\n21:50: ES: Look, humans are unchanged\n21:53: in the midst of this incredible discovery.\n21:55: Do you really think that we're going to get rid of lawyers?\n21:58: No, they're just going to have more sophisticated lawsuits.\n22:01: Do you really think we're going to get rid of politicians?\n22:04: No, they'll just have more platforms to mislead you.\n22:06: Sorry.\n22:08: I mean, I can just go on and on and on.\n22:10: The key thing to understand about this new economics\n22:14: is that we collectively, as a society, are not having enough humans.\n22:19: Look at the reproduction rate in Asia,\n22:21: is essentially 1.0 for two parents.\n22:24: This is not good, right?\n22:26: So for the rest of our lives,\n22:28: the key problem is going to get the people who are productive.\n22:31: That is, in their productive period of lives,\n22:33: more productive to support old people like me, right,\n22:37: who will be bitching that we want more stuff from the younger people.\n22:41: That's how it's going to work.\n22:42: These tools will radically increase that productivity.\n22:46: There's a study that says that we will,\n22:48: under this set of assumptions around agentic AI and discovery\n22:51: and the scale that I'm describing,\n22:53: there's a lot of assumptions\n22:55: that you'll end up\n22:56: with something like 30-percent increase in productivity per year.\n23:00: Having now talked to a bunch of economists,\n23:03: they have no models\n23:04: for what that kind of increase in productivity looks like.\n23:08: We just have never seen it.\n23:09: It didn't occur in any rise of a democracy or a kingdom in our history.\n23:16: It's unbelievable what's going to happen.\n23:19: Hopefully we will get it in the right direction.\n23:22: BS: It is truly unbelievable.\n23:24: Let's bring this home, Eric.\n23:25: You've navigated decades of technological change.\n23:28: For everyone that's navigating this AI transition,\n23:30: technologists, leaders, citizens\n23:33: that are feeling a mix of excitement and anxiety,\n23:36: what is that single piece of wisdom\n23:38: or advice you'd like to offer\n23:40: for navigating this insane moment that we're living through today?\n23:44: ES: So one thing to remember\n23:46: is that this is a marathon, not a sprint.\n23:50: One year I decided to do a 100-mile bike race,\n23:53: which was a mistake.\n23:54: And the idea was, I learned about spin rate.\n23:58: Every day, you get up, and you just keep going.\n23:60: You know, from our work together at Google,\n24:02: that when you’re growing at the rate that we’re growing,\n24:07: you get so much done in a year,\n24:09: you forget how far you went.\n24:12: Humans can't understand that.\n24:14: And we're in this situation\n24:16: where the exponential is moving like this.\n24:18: As this stuff happens quicker,\n24:20: you will forget what was true two years ago or three years ago.\n24:26: That's the key thing.\n24:27: So my advice to you all is ride the wave, but ride it every day.\n24:32: Don't view it as episodic and something you can end,\n24:35: but understand it and build on it.\n24:37: Each and every one of you has a reason to use this technology.\n24:41: If you're an artist, a teacher, a physician,\n24:45: a business person, a technical person.\n24:47: If you're not using this technology,\n24:50: you're not going to be relevant compared to your peer groups\n24:53: and your competitors\n24:54: and the people who want to be successful.\n24:57: Adopt it, and adopt it fast.\n24:59: I have been shocked at how fast these systems --\n25:02: as an aside, my background is enterprise software,\n25:06: and nowadays there's a model Protocol from Anthropic.\n25:10: You can actually connect the model directly into the databases\n25:14: without any of the connectors.\n25:15: I know this sounds nerdy.\n25:16: There's a whole industry there that goes away\n25:19: because you have all this flexibility now.\n25:21: You can just say what you want, and it just produces it.\n25:24: That's an example of a real change in business.\n25:26: There are so many of these things coming every day.\n25:29: BS: Ladies and gentlemen, Eric Schmidt.\n25:31: ES: Thank you very much.\n25:32: (Applause)",
          format: 'subtitle',
          language: 'en',
          status: 'completed',
        },
      ],
      current_content_id: '01980c2c-6f95-750d-b91b-1bbe044ccecb',
      type: 'transcript',
    },
    overview: {
      contents: [
        {
          id: '01980c2e-a476-79f8-a2ff-b4f25e351513',
          block_id: '01980c2e-a470-7546-8a8d-ace045a0f50e',
          raw: 'overview-content',
          plain:
            "The TED talk by Eric Schmidt emphasizes the transformative potential of AI, arguing it is underhyped despite widespread discussions. He highlights the need for energy resources, ethical considerations in AI application, and the importance of adaptation to technological advancements for future productivity.\n\n### The AI Revolution Is Underhyped\n\n#### Non-Human Intelligence Emergence\n\n* The 2016 AlphaGo event demonstrated AI's ability to create novel strategies in established games. [citation-00:05]\n* This moment marked the beginning of significant advancements in AI algorithms. [citation-01:35]\n\n#### Underhyping AI Potential\n\n* Many perceive AI through tools like ChatGPT, yet its broader capabilities remain underestimated. [citation-01:46]\n* Reinforcement learning advances enable strategic planning beyond mere language processing. [citation-02:14]\n\n#### Energy and Resource Challenges\n\n* A projected need for 90 gigawatts of power in America raises concerns about energy supply for growing AI demands. [citation-04:06]\n* Global competition for data center resources is intensifying, especially between the U.S. and China. [citation-11:10]\n\n#### Ethical Dilemmas in AI Development\n\n* The dual-use nature of AI technology presents significant ethical challenges across civilian and military domains. [citation-09:36]\n* Establishing guardrails is crucial to prevent uncontrolled autonomous actions from advanced systems. [citation-09:23]\n\n#### Future Possibilities with AI\n\n* Schmidt envisions a future where human productivity dramatically increases due to advanced technologies, addressing global issues like healthcare and education access. [citation-20:37]\n* Emphasizing continuous adaptation to technological change is vital for individuals and organizations alike to remain relevant. [citation-24:57]",
          format: 'llm-output',
          language: 'en-US',
          status: 'completed',
        },
      ],
      current_content_id: '01980c2e-a476-79f8-a2ff-b4f25e351513',
      type: 'overview',
    },
  },
  UF8uR6Z6KLc: {
    id: 'UF8uR6Z6KLc',
    title: "Steve Jobs' 2005 Stanford Commencement Address",
    author: [
      {
        name: 'Stanford',
        picture:
          'https://cdn.gooo.ai/web-images/1d7307f834f44fdc28e2817065976adb54723c4fbfcfa3ae26cab0b3df7be674',
      },
    ],
    views: '2.1M',
    published_at: 'March 8, 2008',
    created_at: '2025-07-15T03:56:25.457Z',
    hero_image_url:
      'https://cdn.gooo.ai/web-images/75d48a77e1fcf8639fd3a005b740b9adecad3164c01269f8e55b7dd7489e2e75',
    play_url: 'https://www.youtube.com/watch?v=UF8uR6Z6KLc',
    transcript: {
      contents: [
        {
          id: '01980c3a-71e1-7599-b895-28b4db42da01',
          block_id: '01980c3a-31b2-7c6f-a82d-79e9e2a17982',
          plain:
            "[This program is brought to you by Stanford University. Please visit us at stanford.edu.](#00:07)\n\n[Thank you. I am honored to be with you today at your commencement from one of the finest universities in the world. Truth be told, I never graduated from college, and this is the closest I've ever gotten to a college graduation. Today, I want to tell you three stories from my life. That's it. No big deal, just three stories.](#00:20)\n\n[The first story is about connecting the dots. I dropped out of Reed College after the first six months, but then stayed around as a drop-in for another 18 months or so before I really quit. So why did I drop out? It started before I was born. My biological mother was a young, unwed graduate student, and she decided to put me up for adoption. She felt very strongly that I should be adopted by college graduates, so everything was all set for me to be adopted at birth by a lawyer and his wife. Except that when I popped out, they decided at the last minute that they really wanted a girl. So my parents, who were on a waiting list, got a call in the middle of the night asking, \"We have an unexpected baby boy; do you want him?\" They said, \"Of course.\" My biological mother later found out that my mother had never graduated from college and that my father had never graduated from high school. She refused to sign the final adoption papers. She only relented a few months later when my parents promised that I would go to college. This was the start of my life.](#00:54)\n\n[And 17 years later, I did go to college. But I naively chose a college that was almost as expensive as Stanford, and all of my working-class parents' savings were being spent on my college tuition.](#02:11)\n\n[After six months, I couldn't see the value in it. I had no idea what I wanted to do with my life and no idea how college was going to help me figure it out. And here I was spending all of the money my parents had saved their entire life.](#02:28)\n[So I decided to drop out and trust that it would all work out OK. It was pretty scary at the time, but looking back it was one of the best decisions I ever made. The minute I dropped out I could stop taking the required classes that didn't interest me, and begin dropping in on the ones that looked interesting.](#02:43)\n[It wasn't all romantic. I didn't have a dorm room, so I slept on the floor in friends' rooms. I returned coke bottles for the 5 cent deposits to buy food with, and I would walk the 7 miles across town every Sunday night to get one good meal a week at the Hare Krishna temple. I loved it. And much of what I stumbled into by following my curiosity and intuition turned out to be priceless later on.](#03:03)\n[Let me give you one example: Reed College at that time offered perhaps the best calligraphy instruction in the country. Throughout the campus every poster, every label on every drawer, was beautifully hand calligraphed. Because I had dropped out and didn't have to take the normal classes, I decided to take a calligraphy class to learn how to do this.](#03:29)\n[I learned about serif and san serif typefaces, about varying the amount of space between different letter combinations, about what makes great typography great. It was beautiful, historical, artistically subtle in a way that science can't capture, and I found it fascinating. None of this had even a hope of any practical application in my life.](#03:52)\n[But ten years later, when we were designing the first Macintosh computer, it all came back to me. And we designed it all into the Mac. It was the first computer with beautiful typography. If I had never dropped in on that single course in college, the Mac would have never had multiple typefaces or proportionally spaced fonts. And since Windows just copied the Mac, it's likely that no personal computer would have them.](#04:17)\n[If I had never dropped out, I would have never dropped in on this calligraphy class, and personal computers might not have the wonderful typography that they do. Of course it was impossible to connect the dots looking forward when I was in college. But it was very, very clear looking backwards ten years later.](#04:46)\n[Again, you can't connect the dots looking forward; you can only connect them looking backwards. So you have to trust that the dots will somehow connect in your future. You have to trust in something, your gut, destiny, life, karma, whatever.](#05:07)\n[Believing that the dots will connect down the road will give you the confidence to follow your heart, even when it leads you off the well-worn path, and that will make all the difference.](#05:22)\n[My second story is about love and loss. I was lucky I found what I loved to do early in life. Woz and I started Apple in my parents' garage when I was 20. We worked hard, and in 10 years Apple had grown from just the two of us in a garage into a $2 billion company with over 4000 employees. We had just released our finest creation, the Macintosh, a year earlier, and I had just turned 30.](#05:38)\n[And then I got fired. How can you get fired from a company you started? Well, as Apple grew we hired someone who I thought was very talented to run the company with me, and for the first year or so things went well. But then our visions of the future began to diverge and eventually we had a falling out. When we did, our Board of Directors sided with him. So at 30 I was out. And very publicly out. What had been the focus of my entire adult life was gone, and it was devastating.](#06:05)\n[I really didn't know what to do for a few months. I felt that I had let the previous generation of entrepreneurs down - that I had dropped the baton as it was being passed to me. I met with David Packard and Bob Noyce and tried to apologize for screwing up so badly. I was a very public failure, and I even thought about running away from the valley.](#06:38)\n[But something slowly began to dawn on me: I still loved what I did. The turn of events at Apple had not changed that one bit. I had been rejected, but I was still in love. And so I decided to start over.](#06:58)\n[I didn't see it then, but it turned out that getting fired from Apple was the best thing that could have ever happened to me. The heaviness of being successful was replaced by the lightness of being a beginner again, less sure about everything. It freed me to enter one of the most creative periods of my life. During the next five years, I started a company named NeXT, another company named Pixar, and fell in love with an amazing woman who would become my wife.](#07:14)\n[Pixar went on to create the world's first computer-animated feature film, Toy Story, and is now the most successful animation studio in the world. In a remarkable turn of events, Apple bought NeXT.](#07:39)\n[I returned to Apple, and the technology we developed at NeXT is at the heart of Apple's current renaissance. And Laurene and I have a wonderful family together. I'm pretty sure none of this would have happened if I hadn't been fired from Apple. It was awful tasting medicine, but I guess the patient needed it. Sometimes life hits you in the head with a brick. Don't lose faith.](#07:54)\n\n[I'm convinced that the only thing that kept me going was that I loved what I did. You've got to find what you love. And that is as true for your work as it is for your lovers. Your work is going to fill a large part of your life, and the only way to be truly satisfied is to do what you believe is great work. And the only way to do great work is to love what you do. If you haven't found it yet, keep looking. Don't settle. As with all matters of the heart, you'll know when you find it. And, like any great relationship, it just gets better and better as the years roll on. So keep looking. Don't settle.](#08:16)\n\n[My third story is about death. When I was 17, I read a quote that went something like: \"If you live each day as if it was your last, someday you'll most certainly be right.\" It made an impression on me, and since then, for the past 33 years, I have looked in the mirror every morning and asked myself, \"Would I want to do what I am about to do today?\" And whenever the answer has been \"No\" for too many days in a row, I know I need to change something.](#09:05)\n\n[Remembering that I'll be dead soon is the most important tool I've ever encountered to help me make the big choices in life. Because almost everything all external expectations, all pride, all fear of embarrassment or failure - these things just fall away in the face of death, leaving only what is truly important. Remembering that you are going to die is the best way I know to avoid the trap of thinking you have something to lose. You are already naked. There is no reason not to follow your heart.](#09:38)\n\n[About a year ago I was diagnosed with cancer. I had a scan at 7:30 in the morning, and it clearly showed a tumor on my pancreas. I didn't even know what a pancreas was. The doctors told me this was almost certainly a type of cancer that is incurable, and that I should expect to live no longer than three to six months. My doctor advised me to go home and get my affairs in order, which is doctor's code for prepare to die. It means to try to tell your kids everything you thought you'd have the next 10 years to tell them in just a few months. It means to make sure everything is buttoned up so that it will be as easy as possible for your family. It means to say your goodbyes.](#10:13)\n\n[I lived with that diagnosis all day. Later that evening I had a biopsy, where they stuck an endoscope down my throat, through my stomach and into my intestines, put a needle into my pancreas and got a few cells from the tumor. I was sedated, but my wife, who was there, told me that when they viewed the cells under a microscope the doctors started crying because it turned out to be a very rare form of pancreatic cancer that is curable with surgery. I had the surgery and thankfully I'm fine now.](#11:00)\n\n[This was the closest I've been to facing death, and I hope its the closest I get for a few more decades. Having lived through it, I can now say this to you with a bit more certainty than when death was a useful but purely intellectual concept: No one wants to die. Even people who want to go to heaven don't want to die to get there. And yet death is the destination we all share. No one has ever escaped it. And that is as it should be, because Death is very likely the single best invention of Life. It is Life's change agent. It clears out the old to make way for the new. Right now the new is you, but someday not too long from now, you will gradually become the old and be cleared away. Sorry to be so dramatic, but it is quite true.](#11:40)\n\n[Your time is limited, so don't waste it living someone else's life. Don't be trapped by dogma which is living with the results of other people's thinking. Don't let the noise of others' opinions drown out your own inner voice. And most important, have the courage to follow your heart and intuition. They somehow already know what you truly want to become. Everything else is secondary.](#12:31)\n\n[When I was young, there was an amazing publication called The Whole Earth Catalog, which was one of the bibles of my generation. It was created by a fellow named Stewart Brand not far from here in Menlo Park, and he brought it to life with his poetic touch. This was in the late 1960's, before personal computers and desktop publishing, so it was all made with typewriters, scissors, and polaroid cameras. It was sort of like Google in paperback form, 35 years before Google came along overflowing with neat tools, and great notions. Stewart and his team put out several issues of The Whole Earth Catalog.](#13:09)\n\n[And then when it had run its course, they put out a final issue. It was the mid-1970s, and I was your age. On the back cover of their final issue was a photograph of an early morning country road, the kind you might find yourself hitchhiking on if you were so adventurous.](#13:48)\n[Beneath it were the words: \"Stay Hungry. Stay Foolish.\" It was their farewell message as they signed off, and I have always wished that for myself. And now, as you graduate to begin anew, I wish that for you: Stay Hungry. Stay Foolish.](#14:09)\n[Thank you all very much.](#14:32)\n[The preceding program is copyrighted by Stanford University. Please visit us at stanford.edu.](#14:58)",
          format: 'subtitle-formatted',
          raw: 'raw-transcript',
          language: 'en-US',
          status: 'completed',
        },
        {
          id: '01980c3a-31ba-7962-9b9f-913dcf2d9d46',
          block_id: '01980c3a-31b2-7c6f-a82d-79e9e2a17982',
          raw: 'raw-transcript',
          plain:
            "0:07: This program is brought to you by Stanford University.\n0:10: Please visit us at stanford.edu\n0:22: Thank You. I am honored to be with you today at your commencement\n0:30: from one of the finest universities in the world.\n0:36: Truth be told I never graduated from college\n0:42: and this is the closest I've ever gotten to a college graduation.\n0:48: Today I want to tell you three stories from my life. That's it.\n0:52: No big deal. Just three stories.\n0:56: The first story is about connecting the dots.\n1:01: I dropped out of Reed College after the first 6 months,\n1:04: but then stayed around as a drop-in\n1:06: for another 18 months or so before I really quit.\n1:09: So why did I drop out?\n1:12: It started before I was born.\n1:15: My biological mother was a young, unwed graduate student,\n1:19: and she decided to put me up for adoption.\n1:22: She felt very strongly that I should be adopted by college graduates,\n1:26: so everything was all set for me to\n1:29: be adopted at birth by a lawyer and his wife.\n1:32: Except that when I popped out they decided\n1:34: at the last minute that they really wanted a girl.\n1:38: So my parents, who were on a waiting list,\n1:40: got a call in the middle of the night asking: \"We have an unexpected\n1:44: baby boy; do you want him?\"\n1:47: They said: \"Of course.\" My biological mother later found out that\n1:53: my mother had never graduated from college\n1:55: and that my father had never graduated from high school.\n1:59: She refused to sign the final adoption papers.\n2:03: She only relented a few months later when\n2:05: my parents promised that I would go to college. This was the start in my life.\n2:13: And 17 years later I did go to college. But I naively chose a college\n2:19: that was almost as expensive as Stanford,\n2:22: and all of my working-class parents'\n2:24: savings were being spent on my college tuition.\n2:28: After six months, I couldn't see the value in it.\n2:31: I had no idea what I wanted to do with my life\n2:33: and no idea how college was going to help me figure it out.\n2:37: And here I was spending all of the money my parents had saved\n2:40: their entire life.\n2:43: So I decided to drop out and trust that it would all work out OK.\n2:47: It was pretty scary at the time,\n2:49: but looking back it was one of the best decisions I ever made.\n2:54: The minute I dropped out I could stop\n2:57: taking the required classes that didn't interest me,\n2:60: and begin dropping in on the ones that looked interesting.\n3:05: It wasn't all romantic. I didn't have a dorm room,\n3:08: so I slept on the floor in friends' rooms,\n3:11: I returned coke bottles for the 5 cent deposits to buy food with,\n3:15: and I would walk the 7 miles across town every Sunday\n3:17: night to get one good meal a week at the Hare Krishna\n3:21: temple. I loved it.\n3:24: And much of what I stumbled into by following\n3:26: my curiosity and intuition turned out to be priceless later on.\n3:30: Let me give you one example: Reed College at that\n3:35: time offered perhaps the best calligraphy instruction in the country.\n3:38: Throughout the campus every poster, every label on every drawer,\n3:43: was beautifully hand calligraphed.\n3:45: Because I had dropped out and didn't have to take the normal classes,\n3:50: I decided to take a calligraphy class to learn how to do this.\n3:53: I learned about serif and san serif typefaces,\n3:56: about varying the amount of space\n3:58: between different letter combinations,\n3:60: about what makes great typography great.\n4:03: It was beautiful, historical,\n4:06: artistically subtle in a way that science can't capture,\n4:10: and I found it fascinating.\n4:12: None of this had even a hope of any practical application in my life.\n4:18: But ten years later,\n4:19: when we were designing the first Macintosh computer,\n4:22: it all came back to me. And we designed it all into the Mac.\n4:25: It was the first computer with beautiful typography.\n4:29: If I had never dropped in on that single course in college,\n4:33: the Mac would have never had multiple\n4:35: typefaces or proportionally spaced fonts.\n4:37: And since Windows just copied the Mac,\n4:40: it's likely that no personal computer would have them.\n4:47: If I had never dropped out,\n4:51: I would have never dropped in on this calligraphy class,\n4:54: and personal computers might not have the wonderful typography\n4:57: that they do. Of course it was impossible to connect\n5:01: the dots looking forward when I was in college.\n5:02: But it was very, very clear looking backwards ten years later.\n5:07: Again, you can't connect the dots looking forward;\n5:10: you can only connect them looking backwards.\n5:13: So you have to trust that the dots will somehow connect\n5:16: in your future.\n5:17: You have to trust in something, your gut, destiny, life, karma,\n5:21: whatever.\n5:22: Beleiveing that the dots will connect down the road will give you the confidence to follow your heart\n5:28: Even when it leads you off the well worn path, and that will make all the difference.\n5:39: My second story is about love and loss.\n5:44: I was lucky I found what I loved to do early in life.\n5:48: Woz and I started Apple in my parents garage when I was 20.\n5:52: We worked hard, and in 10 years Apple had grown from just the two of\n5:55: us in a garage into a $2 billion company with over 4000 employees.\n5:60: We had just released our finest creation the Macintosh\n6:03: a year earlier, and I had just turned 30.\n6:06: And then I got fired.\n6:09: How can you get fired from a company you started?\n6:12: Well, as Apple grew we hired someone who I thought\n6:16: was very talented to run the company with me,\n6:19: and for the first year or so things went well.\n6:20: But then our visions of the future began\n6:23: to diverge and eventually we had a falling out.\n6:26: When we did, our Board of Directors sided with him.\n6:29: So at 30 I was out. And very publicly out.\n6:33: What had been the focus of my entire adult life was gone,\n6:36: and it was devastating.\n6:39: I really didn't know what to do for a few months.\n6:41: I felt that I had let the previous generation of entrepreneurs\n6:44: down - that I had dropped the baton as it was being passed to me.\n6:47: I met with David Packard and Bob Noyce\n6:51: and tried to apologize for screwing up so badly.\n6:54: I was a very public failure,\n6:56: and I even thought about running away from the valley.\n6:59: But something slowly began to dawn on me I still loved what I did.\n7:04: The turn of events at Apple had not changed that one bit.\n7:08: I had been rejected, but I was still in love.\n7:12: And so I decided to start over.\n7:15: I didn't see it then, but it turned out that getting fired from\n7:18: Apple was the best thing that could have ever happened to me.\n7:21: The heaviness of being successful was\n7:23: replaced by the lightness of being a beginner again,\n7:26: less sure about everything.\n7:27: It freed me to enter one of the most creative periods of my life.\n7:31: During the next five years, I started a company named NeXT,\n7:35: another company named Pixar,\n7:36: and fell in love with an amazing woman who would become my wife.\n7:40: Pixar went on to create the worlds first computer animated feature\n7:43: film, Toy Story,\n7:44: and is now the most successful animation studio in the world.\n7:50: In a remarkable turn of events, Apple bought NeXT,\n7:54: I returned to Apple, and the technology we developed at\n7:57: NeXT is at the heart of Apple's current renaissance.\n7:60: And Laurene and I have a wonderful family together.\n8:04: I'm pretty sure none of this would\n8:06: have happened if I hadn't been fired from Apple.\n8:08: It was awful tasting medicine, but I guess the patient needed it.\n8:12: Sometimes life hits you in the head with a brick. Don't lose faith.\n8:18: I'm convinced that the only thing that kept me going was that I loved\n8:21: what I did. You've got to find what you love.\n8:25: And that is as true for your work as it is for your lovers.\n8:28: Your work is going to fill a large part of your life,\n8:30: and the only way to be truly satisfied\n8:32: is to do what you believe is great work.\n8:35: And the only way to do great work is to love what you do.\n8:38: If you haven't found it yet, keep looking. Don't settle.\n8:44: As with all matters of the heart, you'll know when you find it.\n8:48: And, like any great relationship,\n8:49: it just gets better and better as the years roll on.\n8:52: So keep looking. Don't settle.\n9:05: My third story is about death.\n9:09: When I was 17, I read a quote that went something like:\n9:13: \"If you live each day as if it was your last,\n9:16: someday you'll most certainly be right.\"\n9:21: It made an impression on me, and since then, for the past 33 years,\n9:26: I have looked in the mirror every morning\n9:27: and asked myself: \"If today were the last day of my life,\n9:30: would I want to do what I am about to do today?\"\n9:34: And whenever the answer has been \"No\" for too many days in a row,\n9:38: I know I need to change something.\n9:41: Remembering that I'll be dead soon is the most important\n9:44: tool I've ever encountered to help me make the big choices in life.\n9:48: Because almost everything all external expectations, all pride,\n9:53: all fear of embarrassment or failure -\n9:55: these things just fall away in the face of death,\n9:58: leaving only what is truly important.\n10:01: Remembering that you are going to die is the best\n10:04: way I know to avoid the trap of thinking you have something to lose.\n10:08: You are already naked. There is no reason not to follow your heart.\n10:14: About a year ago I was diagnosed with cancer.\n10:17: I had a scan at 7:30 in the morning,\n10:20: and it clearly showed a tumor on my pancreas.\n10:24: I didn't even know what a pancreas was.\n10:26: The doctors told me this was almost\n10:29: certainly a type of cancer that is incurable,\n10:31: and that I should expect to live no longer than three to six months.\n10:36: My doctor advised me to go home and get my affairs in order,\n10:40: which is doctor's code for prepare to die.\n10:43: It means to try to tell your kids everything you thought\n10:48: you'd have the next 10 years to tell them in just a few months.\n10:52: It means to make sure everything is buttoned\n10:54: up so that it will be as easy as possible for your family.\n10:57: It means to say your goodbyes.\n11:01: I lived with that diagnosis all day.\n11:04: Later that evening I had a biopsy,\n11:07: where they stuck an endoscope down my throat,\n11:09: through my stomach and into my intestines,\n11:11: put a needle into my pancreas and got a few cells from the tumor.\n11:14: I was sedated, but my wife, who was there,\n11:19: told me that when they viewed the cells under a microscope\n11:22: the doctors started crying because it turned out to be\n11:25: a very rare form of pancreatic cancer that is curable with surgery.\n11:29: I had the surgery and thankfully I'm fine now.\n11:41: This was the closest I've been to facing death,\n11:44: and I hope its the closest I get for a few more decades.\n11:47: Having lived through it,\n11:49: I can now say this to you with a bit more certainty than when\n11:51: death was a useful but purely intellectual concept:\n11:55: No one wants to die.\n11:58: Even people who want to go to heaven don't want to die to get there.\n12:02: And yet death is the destination we all share.\n12:06: No one has ever escaped it. And that is as it should be,\n12:10: because Death is very likely the single best invention of Life.\n12:15: It is Life's change agent.\n12:17: It clears out the old to make way for the new.\n12:20: Right now the new is you, but someday not too long from now,\n12:25: you will gradually become the old and be cleared away.\n12:29: Sorry to be so dramatic, but it is quite true.\n12:32: Your time is limited, so don't waste it living someone else's life.\n12:38: Don't be trapped by dogma which is living\n12:40: with the results of other people's thinking.\n12:43: Don't let the noise of others' opinions drown out your own inner\n12:46: voice. And most important,\n12:49: have the courage to follow your heart and intuition.\n12:52: They somehow already know what you truly want to become.\n12:56: Everything else is secondary.\n13:10: When I was young,\n13:11: there was an amazing publication called The Whole Earth Catalog,\n13:15: which was one of the bibles of my generation.\n13:18: It was created by a fellow named Stewart Brand not far from here\n13:22: in Menlo Park, and he brought it to life with his poetic touch.\n13:26: This was in the late 1960's,\n13:28: before personal computers and desktop publishing,\n13:30: so it was all made with typewriters, scissors, and polaroid cameras.\n13:35: It was sort of like Google in paperback form,\n13:37: 35 years before Google came along: it was idealistic,\n13:42: overflowing with neat tools, and great notions.\n13:45: Stewart and his team put out several\n13:47: issues of The Whole Earth Catalog,\n13:48: and then when it had run its course, they put out a final issue.\n13:53: It was the mid-1970s, and I was your age.\n13:59: On the back cover of their final issue\n14:00: was a photograph of an early morning country road,\n14:04: the kind you might find yourself\n14:05: hitchhiking on if you were so adventurous.\n14:09: Beneath it were the words: \"Stay Hungry. Stay Foolish.\"\n14:13: It was their farewell message as they signed off. Stay Hungry.\n14:19: Stay Foolish. And I have always wished that for myself.\n14:24: And now, as you graduate to begin anew, I wish that for you.\n14:29: Stay Hungry. Stay Foolish.\n14:32: Thank you all very much.\n14:58: The preceding program is copyrighted by Stanford University.\n15:01: Please visit us at stanford.edu",
          format: 'subtitle',
          language: 'en',
          status: 'completed',
        },
      ],
      current_content_id: '01980c3a-71e1-7599-b895-28b4db42da01',
      type: 'transcript',
    },
    overview: {
      contents: [
        {
          id: '01980c3a-67ef-7a50-ad45-eb50ac05fea4',
          block_id: '01980c3a-67ea-7a9d-ba2a-3a9585127de3',
          raw: 'overview-content',
          plain:
            "Steve Jobs' 2005 Stanford Commencement Address emphasizes the importance of connecting life experiences, pursuing passion, and facing mortality. Through three personal stories, he illustrates how trusting one's intuition can lead to success and fulfillment, urging graduates to \"stay hungry\" and \"stay foolish.\"\n\n### Life Lessons from Experience\n\n#### Connecting the Dots\n\n* Dropping out of college allowed Jobs to pursue interests that later influenced his work at Apple. [citation-04:57]\n\n* Trusting in life's journey enables individuals to connect past experiences with future opportunities. [citation-05:16]\n\n#### Love and Loss\n\n* Jobs founded Apple but faced a setback when he was fired, leading him to discover new passions. [citation-06:09]\n\n* His love for his work remained unchanged despite challenges, highlighting the importance of following one's passion. [citation-08:21]\n\n#### Facing Death\n\n* A cancer diagnosis brought clarity about life's priorities, emphasizing the need to live authentically. [citation-10:14]\n\n* Recognizing death as a natural part of life encourages individuals to follow their hearts without fear. [citation-12:49]\n\n#### Final Thoughts\n\n* The Whole Earth Catalog's farewell message inspired Jobs’ mantra: “Stay Hungry. Stay Foolish.” [citation-14:19] \n\n* He encourages graduates to embrace curiosity and creativity as they embark on their journeys. [citation-14:29]",
          format: 'llm-output',
          language: 'en-US',
          status: 'completed',
        },
      ],
      current_content_id: '01980c3a-67ef-7a50-ad45-eb50ac05fea4',
      type: 'overview',
    },
  },
};

// === 工具函数 ===

/**
 * 获取所有示例视频（用于Examples组件）
 */
export function getExampleVideos(): ExampleVideo[] {
  return EXAMPLE_VIDEOS;
}

/**
 * 获取所有预设视频的ID列表
 */
export function getPresetVideoIds(): string[] {
  return Object.keys(PRESET_VIDEOS);
}

/**
 * 检查是否为预设视频
 */
export function isPresetVideo(videoId: string): boolean {
  return videoId in PRESET_VIDEOS;
}

/**
 * 获取预设视频数据
 */
export function getPresetVideo(videoId: string): WatchVideoData | undefined {
  return PRESET_VIDEOS[videoId];
}
